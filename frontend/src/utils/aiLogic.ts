import { Agent, Message, Discussion, ModeratorAction, ModeratorConfig } from '../types';
import { llmService } from '../services/llmService';
// 生成智能体消息
export async function generateMessage(
  agent: Agent,
  discussion: Discussion,
  topic: string,
  lastMessages: Message[] = []
): Promise<string> {
  // 智能体必须配置LLM才能生成消息
  if (!agent.llmConfig) {
    throw new Error(`智能体 ${agent.name} 未配置LLM，无法生成消息`);
  }

  try {
    return await llmService.generateAgentMessage(agent, discussion, topic, lastMessages);
  } catch (error) {
    console.error(`智能体 ${agent.name} 的LLM调用失败:`, error);
    throw new Error(`智能体 ${agent.name} 的LLM调用失败: ${error instanceof Error ? error.message : '未知错误'}`);
  }
}


// 计算讨论共识度
export function calculateConsensus(messages: Message[], agents: Agent[]): number {
  if (messages.length < 3) return 0;
  
  const recentMessages = messages.slice(-10); // 分析最近10条消息
  
  // 计算各项指标
  const agreementRatio = recentMessages.filter(m => m.type === 'agreement').length / recentMessages.length;
  const disagreementRatio = recentMessages.filter(m => m.type === 'disagreement').length / recentMessages.length;
  const questionRatio = recentMessages.filter(m => m.type === 'question').length / recentMessages.length;
  
  // 参与度分析
  const participantActivity = new Map<string, number>();
  recentMessages.forEach(m => {
    participantActivity.set(m.agentId, (participantActivity.get(m.agentId) || 0) + 1);
  });
  
  const activeParticipants = participantActivity.size;
  const totalParticipants = agents.filter(a => a.isActive).length;
  const participationRatio = activeParticipants / totalParticipants;
  

  
  // 综合计算共识度
  let consensusScore = 0;
  
  // 高同意比例增加共识度
  consensusScore += agreementRatio * 40;
  
  // 低争议比例增加共识度
  consensusScore += (1 - disagreementRatio) * 30;
  
  // 适量问题表示深入讨论，但过多问题表示混乱
  consensusScore += (questionRatio > 0.1 && questionRatio < 0.3) ? 15 : 0;
  
  // 参与度
  consensusScore += participationRatio * 15;
  
  return Math.min(100, Math.max(0, consensusScore));
}
// 生成共识结论
export function generateConsensus(messages: Message[], topic: string): string {
  const recentMessages = messages.slice(-15);
  const statements = recentMessages.filter(m => m.type === 'statement' || m.type === 'agreement');
  
  if (statements.length === 0) {
    return `关于"${topic}"，参与者需要更多时间来达成共识。`;
  }
  
  // 简单的关键词提取和总结
  const keywords = extractKeywords(statements.map(m => m.content));
  
  return `经过充分讨论，大家就"${topic}"达成了共识：${keywords.slice(0, 3).join('、')}是关键要素，需要重点关注和实施。`;
}
function extractKeywords(_contents: string[]): string[] {
  const keywords = ['技术创新', '用户体验', '市场需求', '成本控制', '时间规划', '质量保证', '团队合作', '数据分析'];
  return keywords.filter(() => Math.random() > 0.6).slice(0, 5);
}
// 智能体发言间隔控制
export function getNextSpeaker(
  participants: string[],
  messages: Message[],
  mode: 'moderator' | 'free'
): string | null {
  if (participants.length === 0) return null;
  
  if (mode === 'moderator') {
    // 主持人模式：轮流发言
    const recentSpeakers = messages.slice(-participants.length).map(m => m.agentId);
    const unspokenParticipants = participants.filter(id => !recentSpeakers.includes(id));
    
    if (unspokenParticipants.length > 0) {
      return unspokenParticipants[0];
    }
    return participants[0]; // 重新开始轮流
  } else {
    // 自由讨论模式：基于活跃度和随机性
    const recentMessages = messages.slice(-10);
    const activityCount = new Map<string, number>();
    
    participants.forEach(id => activityCount.set(id, 0));
    recentMessages.forEach(m => {
      if (participants.includes(m.agentId)) {
        activityCount.set(m.agentId, (activityCount.get(m.agentId) || 0) + 1);
      }
    });
    
    // 选择活跃度较低的参与者
    const sortedByActivity = [...activityCount.entries()]
      .sort(([,a], [,b]) => a - b);
    
    const leastActive = sortedByActivity.slice(0, Math.ceil(participants.length / 2));
    const randomChoice = leastActive[Math.floor(Math.random() * leastActive.length)];
    
    return randomChoice[0];
  }
}

// ============= 主持人相关功能 =============

// 生成主持人实时总结
export async function generateModeratorSummary(
  moderator: Agent,
  discussion: Discussion,
  recentMessages: Message[]
): Promise<string> {
  if (!moderator.llmConfig) {
    throw new Error(`主持人 ${moderator.name} 未配置LLM，无法生成总结`);
  }

  try {
    const prompt = `作为讨论主持人，请对以下最近的讨论内容进行简洁总结：

讨论主题：${discussion.topic}
最近消息：
${recentMessages.map(msg => `- ${msg.content}`).join('\n')}

请提供一个简洁的总结，突出关键观点和进展：`;

    return await llmService.generateResponse(moderator.llmConfig, prompt);
  } catch (error) {
    console.error(`主持人总结生成失败:`, error);
    return `总结生成失败：${error instanceof Error ? error.message : '未知错误'}`;
  }
}

// 计算话题相关性
export async function calculateTopicRelevance(
  moderator: Agent,
  discussion: Discussion,
  recentMessages: Message[]
): Promise<number> {
  if (!moderator.llmConfig) {
    return 0.8; // 默认相关性
  }

  try {
    const prompt = `作为讨论主持人，请评估以下讨论内容与主题的相关性：

讨论主题：${discussion.topic}
最近消息：
${recentMessages.map(msg => `- ${msg.content}`).join('\n')}

请给出相关性评分（0-1之间的数字，1表示完全相关，0表示完全无关）。
只需要返回数字，不需要解释：`;

    const response = await llmService.generateResponse(moderator.llmConfig, prompt);
    const score = parseFloat(response.trim());
    return isNaN(score) ? 0.8 : Math.max(0, Math.min(1, score));
  } catch (error) {
    console.error(`话题相关性计算失败:`, error);
    return 0.8; // 默认相关性
  }
}

// 智能选择下一个发言人
export async function selectNextSpeaker(
  moderator: Agent,
  discussion: Discussion,
  participants: Agent[],
  recentMessages: Message[]
): Promise<string | null> {
  if (!moderator.llmConfig || participants.length === 0) {
    return getNextSpeaker(participants.map(p => p.id), recentMessages, discussion.mode);
  }

  try {
    const participantInfo = participants.map(p =>
      `${p.name}（专业：${p.expertise.join('、')}，思维：${p.thinkingStyle}）`
    ).join('\n');

    const prompt = `作为讨论主持人，请根据以下信息选择下一个最适合发言的参与者：

讨论主题：${discussion.topic}
参与者信息：
${participantInfo}

最近讨论内容：
${recentMessages.slice(-5).map(msg => {
  const speaker = participants.find(p => p.id === msg.agentId);
  return `${speaker?.name || '未知'}: ${msg.content}`;
}).join('\n')}

请选择最适合继续这个话题的参与者，只需要返回参与者的名字：`;

    const response = await llmService.generateResponse(moderator.llmConfig, prompt);
    const selectedName = response.trim();
    const selectedAgent = participants.find(p => p.name === selectedName);

    return selectedAgent?.id || getNextSpeaker(participants.map(p => p.id), recentMessages, discussion.mode);
  } catch (error) {
    console.error(`智能发言人选择失败:`, error);
    return getNextSpeaker(participants.map(p => p.id), recentMessages, discussion.mode);
  }
}

// 生成主持人引导发言
export async function generateModeratorIntervention(
  moderator: Agent,
  discussion: Discussion,
  reason: 'off_topic' | 'low_activity' | 'conflict_resolution' | 'summary_request'
): Promise<string> {
  if (!moderator.llmConfig) {
    const defaultMessages = {
      off_topic: '让我们回到主题上来，继续讨论相关内容。',
      low_activity: '大家可以分享更多想法，让讨论更加活跃。',
      conflict_resolution: '我们来总结一下不同的观点，寻找共同点。',
      summary_request: '让我总结一下到目前为止的讨论要点。'
    };
    return defaultMessages[reason];
  }

  try {
    const prompts = {
      off_topic: `作为讨论主持人，发现讨论偏离了主题"${discussion.topic}"。请生成一段引导语，礼貌地将讨论拉回正轨。`,
      low_activity: `作为讨论主持人，发现讨论活跃度较低。请生成一段鼓励性的引导语，激发参与者的讨论热情。`,
      conflict_resolution: `作为讨论主持人，发现参与者之间存在分歧。请生成一段中性的引导语，帮助化解冲突并推进讨论。`,
      summary_request: `作为讨论主持人，需要对当前讨论进行阶段性总结。请生成一段总结性的引导语。`
    };

    const response = await llmService.generateResponse(moderator.llmConfig, prompts[reason]);
    return response.trim();
  } catch (error) {
    console.error(`主持人引导语生成失败:`, error);
    return '让我们继续讨论，保持专注和建设性。';
  }
}

// 判断是否应该终止讨论
export async function shouldTerminateDiscussion(
  moderator: Agent,
  discussion: Discussion,
  config: ModeratorConfig
): Promise<{ shouldTerminate: boolean; reason?: string }> {
  // 检查基本条件
  if (discussion.consensusScore > 85) {
    return { shouldTerminate: true, reason: '达成共识' };
  }

  if (discussion.moderatorInterventions >= config.maxInterventions && config.maxInterventions > 0) {
    return { shouldTerminate: true, reason: '主持人终止' };
  }

  if (!config.autoTerminate || !moderator.llmConfig) {
    return { shouldTerminate: false };
  }

  try {
    const recentMessages = discussion.messages.slice(-10);
    const prompt = `作为讨论主持人，请判断以下讨论是否应该结束：

讨论主题：${discussion.topic}
当前共识度：${discussion.consensusScore}%
话题相关性：${discussion.topicRelevanceScore}
干预次数：${discussion.moderatorInterventions}

最近讨论内容：
${recentMessages.map(msg => `- ${msg.content}`).join('\n')}

请判断是否应该结束讨论，只需要回答"是"或"否"：`;

    const response = await llmService.generateResponse(moderator.llmConfig, prompt);
    const shouldTerminate = response.trim().toLowerCase().includes('是');

    return {
      shouldTerminate,
      reason: shouldTerminate ? '主持人终止' : undefined
    };
  } catch (error) {
    console.error(`讨论终止判断失败:`, error);
    return { shouldTerminate: false };
  }
}

// 创建主持人操作记录
export function createModeratorAction(
  discussionId: string,
  moderatorId: string,
  actionType: ModeratorAction['actionType'],
  actionContent: string,
  contextData?: any,
  consensusScoreBefore?: number,
  consensusScoreAfter?: number
): Omit<ModeratorAction, 'id' | 'timestamp'> {
  return {
    discussionId,
    moderatorId,
    actionType,
    actionContent,
    contextData,
    consensusScoreBefore,
    consensusScoreAfter
  };
}