import { LLMConfig, LLMRequest, LLMResponse, Agent, Message, Discussion } from '../types';

// LLM服务类
export class LLMService {
  private static instance: LLMService;
  
  public static getInstance(): LLMService {
    if (!LLMService.instance) {
      LLMService.instance = new LLMService();
    }
    return LLMService.instance;
  }

  // 调用LLM API
  async callLLM(config: LLMConfig, request: LLMRequest): Promise<LLMResponse> {
    try {
      const response = await this.makeAPICall(config, request);
      return response;
    } catch (error) {
      console.error('LLM API调用失败:', error);
      throw new Error(`LLM调用失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  }

  // 生成简单响应（用于主持人功能）
  async generateResponse(config: LLMConfig, prompt: string): Promise<string> {
    const request: LLMRequest = {
      messages: [
        { role: 'user', content: prompt }
      ],
      temperature: config.temperature || 0.7,
      maxTokens: config.maxTokens || 500,
      model: config.model
    };

    const response = await this.callLLM(config, request);
    return response.content;
  }

  // 生成智能体消息
  async generateAgentMessage(
    agent: Agent,
    _discussion: Discussion,
    topic: string,
    lastMessages: Message[] = []
  ): Promise<string> {
    const systemPrompt = this.buildSystemPrompt(agent, topic);
    const conversationHistory = this.buildConversationHistory(lastMessages, agent.id);

    const request: LLMRequest = {
      messages: [
        { role: 'system', content: systemPrompt },
        ...conversationHistory,
        { role: 'user', content: `请基于当前讨论情况，就"${topic}"这个话题发表你的观点。` }
      ],
      temperature: agent.llmConfig.temperature || 0.7,
      maxTokens: agent.llmConfig.maxTokens || 500,
      model: agent.llmConfig.model
    };

    const response = await this.callLLM(agent.llmConfig, request);
    return response.content;
  }

  // 构建系统提示词
  private buildSystemPrompt(agent: Agent, topic: string): string {
    const basePrompt = `你是一个名为"${agent.name}"的智能体，正在参与关于"${topic}"的讨论。

你的特征：
- 专业领域：${agent.expertise.join('、')}
- 思维方式：${agent.thinkingStyle}
- 性格特征：${agent.personality}
- 可用工具：${agent.tools.join('、')}

请根据你的专业背景和性格特征参与讨论，保持角色一致性。你的回复应该：
1. 体现你的专业知识和思维方式
2. 符合你的性格特征
3. 简洁明了，通常在100-200字之间
4. 针对讨论话题提供有价值的观点

${agent.llmConfig.systemPrompt || ''}`;

    return basePrompt;
  }

  // 构建对话历史
  private buildConversationHistory(messages: Message[], currentAgentId: string): Array<{role: 'user' | 'assistant', content: string}> {
    const recentMessages = messages.slice(-10); // 只取最近10条消息
    
    return recentMessages.map(msg => ({
      role: msg.agentId === currentAgentId ? 'assistant' : 'user',
      content: `${msg.agentId === currentAgentId ? '我' : '其他参与者'}：${msg.content}`
    }));
  }

  // 实际的API调用
  private async makeAPICall(config: LLMConfig, request: LLMRequest): Promise<LLMResponse> {
    const url = this.getAPIUrl(config);
    const headers = this.getAPIHeaders(config);
    const body = this.formatRequestBody(config, request);

    const response = await fetch(url, {
      method: 'POST',
      headers,
      body: JSON.stringify(body)
    });

    if (!response.ok) {
      throw new Error(`API请求失败: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    return this.parseResponse(config, data);
  }

  // 获取API URL
  private getAPIUrl(config: LLMConfig): string {
    if (config.baseURL) {
      return `${config.baseURL}/chat/completions`;
    }

    switch (config.provider) {
      case 'openai':
        return 'https://api.openai.com/v1/chat/completions';
      case 'anthropic':
        return 'https://api.anthropic.com/v1/messages';
      case 'azure':
        return `${config.baseURL}/openai/deployments/${config.model}/chat/completions?api-version=2023-12-01-preview`;
      default:
        throw new Error(`不支持的提供商: ${config.provider}`);
    }
  }

  // 获取API请求头
  private getAPIHeaders(config: LLMConfig): Record<string, string> {
    const headers: Record<string, string> = {
      'Content-Type': 'application/json'
    };

    switch (config.provider) {
      case 'openai':
      case 'azure':
      case 'custom':
        headers['Authorization'] = `Bearer ${config.apiKey}`;
        break;
      case 'anthropic':
        headers['x-api-key'] = config.apiKey;
        headers['anthropic-version'] = '2023-06-01';
        break;
    }

    return headers;
  }

  // 格式化请求体
  private formatRequestBody(config: LLMConfig, request: LLMRequest): any {
    switch (config.provider) {
      case 'anthropic':
        return {
          model: request.model || config.model,
          max_tokens: request.maxTokens || 1000,
          temperature: request.temperature || 0.7,
          messages: request.messages.filter(m => m.role !== 'system'),
          system: request.messages.find(m => m.role === 'system')?.content || ''
        };
      default:
        return {
          model: request.model || config.model,
          messages: request.messages,
          temperature: request.temperature || 0.7,
          max_tokens: request.maxTokens || 1000
        };
    }
  }

  // 解析响应
  private parseResponse(config: LLMConfig, data: any): LLMResponse {
    switch (config.provider) {
      case 'anthropic':
        return {
          content: data.content[0]?.text || '',
          usage: data.usage ? {
            promptTokens: data.usage.input_tokens,
            completionTokens: data.usage.output_tokens,
            totalTokens: data.usage.input_tokens + data.usage.output_tokens
          } : undefined,
          model: data.model
        };
      default:
        return {
          content: data.choices[0]?.message?.content || '',
          usage: data.usage ? {
            promptTokens: data.usage.prompt_tokens,
            completionTokens: data.usage.completion_tokens,
            totalTokens: data.usage.total_tokens
          } : undefined,
          model: data.model
        };
    }
  }

  // 测试LLM配置
  async testLLMConfig(config: LLMConfig): Promise<boolean> {
    try {
      const testRequest: LLMRequest = {
        messages: [
          { role: 'user', content: '请回复"测试成功"' }
        ],
        temperature: 0.1,
        maxTokens: 10
      };

      await this.callLLM(config, testRequest);
      return true;
    } catch (error) {
      console.error('LLM配置测试失败:', error);
      return false;
    }
  }
}

// 导出单例实例
export const llmService = LLMService.getInstance();
