var Qc=Object.defineProperty;var Kc=(e,t,n)=>t in e?Qc(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n;var Le=(e,t,n)=>(Kc(e,typeof t!="symbol"?t+"":t,n),n);(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const s of document.querySelectorAll('link[rel="modulepreload"]'))r(s);new MutationObserver(s=>{for(const i of s)if(i.type==="childList")for(const o of i.addedNodes)o.tagName==="LINK"&&o.rel==="modulepreload"&&r(o)}).observe(document,{childList:!0,subtree:!0});function n(s){const i={};return s.integrity&&(i.integrity=s.integrity),s.referrerPolicy&&(i.referrerPolicy=s.referrerPolicy),s.crossOrigin==="use-credentials"?i.credentials="include":s.crossOrigin==="anonymous"?i.credentials="omit":i.credentials="same-origin",i}function r(s){if(s.ep)return;s.ep=!0;const i=n(s);fetch(s.href,i)}})();function Gc(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var ba={exports:{}},Cs={},Ea={exports:{}},I={};/**
 * @license React
 * react.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var pr=Symbol.for("react.element"),Yc=Symbol.for("react.portal"),Xc=Symbol.for("react.fragment"),qc=Symbol.for("react.strict_mode"),Zc=Symbol.for("react.profiler"),Jc=Symbol.for("react.provider"),ed=Symbol.for("react.context"),td=Symbol.for("react.forward_ref"),nd=Symbol.for("react.suspense"),rd=Symbol.for("react.memo"),sd=Symbol.for("react.lazy"),co=Symbol.iterator;function ld(e){return e===null||typeof e!="object"?null:(e=co&&e[co]||e["@@iterator"],typeof e=="function"?e:null)}var La={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},Ta=Object.assign,Ma={};function Cn(e,t,n){this.props=e,this.context=t,this.refs=Ma,this.updater=n||La}Cn.prototype.isReactComponent={};Cn.prototype.setState=function(e,t){if(typeof e!="object"&&typeof e!="function"&&e!=null)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")};Cn.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")};function Da(){}Da.prototype=Cn.prototype;function hi(e,t,n){this.props=e,this.context=t,this.refs=Ma,this.updater=n||La}var gi=hi.prototype=new Da;gi.constructor=hi;Ta(gi,Cn.prototype);gi.isPureReactComponent=!0;var fo=Array.isArray,_a=Object.prototype.hasOwnProperty,xi={current:null},Aa={key:!0,ref:!0,__self:!0,__source:!0};function Pa(e,t,n){var r,s={},i=null,o=null;if(t!=null)for(r in t.ref!==void 0&&(o=t.ref),t.key!==void 0&&(i=""+t.key),t)_a.call(t,r)&&!Aa.hasOwnProperty(r)&&(s[r]=t[r]);var a=arguments.length-2;if(a===1)s.children=n;else if(1<a){for(var u=Array(a),m=0;m<a;m++)u[m]=arguments[m+2];s.children=u}if(e&&e.defaultProps)for(r in a=e.defaultProps,a)s[r]===void 0&&(s[r]=a[r]);return{$$typeof:pr,type:e,key:i,ref:o,props:s,_owner:xi.current}}function id(e,t){return{$$typeof:pr,type:e.type,key:t,ref:e.ref,props:e.props,_owner:e._owner}}function yi(e){return typeof e=="object"&&e!==null&&e.$$typeof===pr}function od(e){var t={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,function(n){return t[n]})}var mo=/\/+/g;function Vs(e,t){return typeof e=="object"&&e!==null&&e.key!=null?od(""+e.key):t.toString(36)}function Or(e,t,n,r,s){var i=typeof e;(i==="undefined"||i==="boolean")&&(e=null);var o=!1;if(e===null)o=!0;else switch(i){case"string":case"number":o=!0;break;case"object":switch(e.$$typeof){case pr:case Yc:o=!0}}if(o)return o=e,s=s(o),e=r===""?"."+Vs(o,0):r,fo(s)?(n="",e!=null&&(n=e.replace(mo,"$&/")+"/"),Or(s,t,n,"",function(m){return m})):s!=null&&(yi(s)&&(s=id(s,n+(!s.key||o&&o.key===s.key?"":(""+s.key).replace(mo,"$&/")+"/")+e)),t.push(s)),1;if(o=0,r=r===""?".":r+":",fo(e))for(var a=0;a<e.length;a++){i=e[a];var u=r+Vs(i,a);o+=Or(i,t,n,u,s)}else if(u=ld(e),typeof u=="function")for(e=u.call(e),a=0;!(i=e.next()).done;)i=i.value,u=r+Vs(i,a++),o+=Or(i,t,n,u,s);else if(i==="object")throw t=String(e),Error("Objects are not valid as a React child (found: "+(t==="[object Object]"?"object with keys {"+Object.keys(e).join(", ")+"}":t)+"). If you meant to render a collection of children, use an array instead.");return o}function jr(e,t,n){if(e==null)return e;var r=[],s=0;return Or(e,r,"","",function(i){return t.call(n,i,s++)}),r}function ad(e){if(e._status===-1){var t=e._result;t=t(),t.then(function(n){(e._status===0||e._status===-1)&&(e._status=1,e._result=n)},function(n){(e._status===0||e._status===-1)&&(e._status=2,e._result=n)}),e._status===-1&&(e._status=0,e._result=t)}if(e._status===1)return e._result.default;throw e._result}var me={current:null},Ur={transition:null},ud={ReactCurrentDispatcher:me,ReactCurrentBatchConfig:Ur,ReactCurrentOwner:xi};function Ia(){throw Error("act(...) is not supported in production builds of React.")}I.Children={map:jr,forEach:function(e,t,n){jr(e,function(){t.apply(this,arguments)},n)},count:function(e){var t=0;return jr(e,function(){t++}),t},toArray:function(e){return jr(e,function(t){return t})||[]},only:function(e){if(!yi(e))throw Error("React.Children.only expected to receive a single React element child.");return e}};I.Component=Cn;I.Fragment=Xc;I.Profiler=Zc;I.PureComponent=hi;I.StrictMode=qc;I.Suspense=nd;I.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=ud;I.act=Ia;I.cloneElement=function(e,t,n){if(e==null)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+e+".");var r=Ta({},e.props),s=e.key,i=e.ref,o=e._owner;if(t!=null){if(t.ref!==void 0&&(i=t.ref,o=xi.current),t.key!==void 0&&(s=""+t.key),e.type&&e.type.defaultProps)var a=e.type.defaultProps;for(u in t)_a.call(t,u)&&!Aa.hasOwnProperty(u)&&(r[u]=t[u]===void 0&&a!==void 0?a[u]:t[u])}var u=arguments.length-2;if(u===1)r.children=n;else if(1<u){a=Array(u);for(var m=0;m<u;m++)a[m]=arguments[m+2];r.children=a}return{$$typeof:pr,type:e.type,key:s,ref:i,props:r,_owner:o}};I.createContext=function(e){return e={$$typeof:ed,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null},e.Provider={$$typeof:Jc,_context:e},e.Consumer=e};I.createElement=Pa;I.createFactory=function(e){var t=Pa.bind(null,e);return t.type=e,t};I.createRef=function(){return{current:null}};I.forwardRef=function(e){return{$$typeof:td,render:e}};I.isValidElement=yi;I.lazy=function(e){return{$$typeof:sd,_payload:{_status:-1,_result:e},_init:ad}};I.memo=function(e,t){return{$$typeof:rd,type:e,compare:t===void 0?null:t}};I.startTransition=function(e){var t=Ur.transition;Ur.transition={};try{e()}finally{Ur.transition=t}};I.unstable_act=Ia;I.useCallback=function(e,t){return me.current.useCallback(e,t)};I.useContext=function(e){return me.current.useContext(e)};I.useDebugValue=function(){};I.useDeferredValue=function(e){return me.current.useDeferredValue(e)};I.useEffect=function(e,t){return me.current.useEffect(e,t)};I.useId=function(){return me.current.useId()};I.useImperativeHandle=function(e,t,n){return me.current.useImperativeHandle(e,t,n)};I.useInsertionEffect=function(e,t){return me.current.useInsertionEffect(e,t)};I.useLayoutEffect=function(e,t){return me.current.useLayoutEffect(e,t)};I.useMemo=function(e,t){return me.current.useMemo(e,t)};I.useReducer=function(e,t,n){return me.current.useReducer(e,t,n)};I.useRef=function(e){return me.current.useRef(e)};I.useState=function(e){return me.current.useState(e)};I.useSyncExternalStore=function(e,t,n){return me.current.useSyncExternalStore(e,t,n)};I.useTransition=function(){return me.current.useTransition()};I.version="18.3.1";Ea.exports=I;var T=Ea.exports;const za=Gc(T);/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var cd=T,dd=Symbol.for("react.element"),fd=Symbol.for("react.fragment"),md=Object.prototype.hasOwnProperty,pd=cd.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,hd={key:!0,ref:!0,__self:!0,__source:!0};function Ra(e,t,n){var r,s={},i=null,o=null;n!==void 0&&(i=""+n),t.key!==void 0&&(i=""+t.key),t.ref!==void 0&&(o=t.ref);for(r in t)md.call(t,r)&&!hd.hasOwnProperty(r)&&(s[r]=t[r]);if(e&&e.defaultProps)for(r in t=e.defaultProps,t)s[r]===void 0&&(s[r]=t[r]);return{$$typeof:dd,type:e,key:i,ref:o,props:s,_owner:pd.current}}Cs.Fragment=fd;Cs.jsx=Ra;Cs.jsxs=Ra;ba.exports=Cs;var l=ba.exports,hl={},$a={exports:{}},be={},Oa={exports:{}},Ua={};/**
 * @license React
 * scheduler.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */(function(e){function t(E,D){var P=E.length;E.push(D);e:for(;0<P;){var G=P-1>>>1,ee=E[G];if(0<s(ee,D))E[G]=D,E[P]=ee,P=G;else break e}}function n(E){return E.length===0?null:E[0]}function r(E){if(E.length===0)return null;var D=E[0],P=E.pop();if(P!==D){E[0]=P;e:for(var G=0,ee=E.length,vr=ee>>>1;G<vr;){var Mt=2*(G+1)-1,Fs=E[Mt],Dt=Mt+1,wr=E[Dt];if(0>s(Fs,P))Dt<ee&&0>s(wr,Fs)?(E[G]=wr,E[Dt]=P,G=Dt):(E[G]=Fs,E[Mt]=P,G=Mt);else if(Dt<ee&&0>s(wr,P))E[G]=wr,E[Dt]=P,G=Dt;else break e}}return D}function s(E,D){var P=E.sortIndex-D.sortIndex;return P!==0?P:E.id-D.id}if(typeof performance=="object"&&typeof performance.now=="function"){var i=performance;e.unstable_now=function(){return i.now()}}else{var o=Date,a=o.now();e.unstable_now=function(){return o.now()-a}}var u=[],m=[],x=1,y=null,v=3,w=!1,j=!1,p=!1,g=typeof setTimeout=="function"?setTimeout:null,c=typeof clearTimeout=="function"?clearTimeout:null,d=typeof setImmediate<"u"?setImmediate:null;typeof navigator<"u"&&navigator.scheduling!==void 0&&navigator.scheduling.isInputPending!==void 0&&navigator.scheduling.isInputPending.bind(navigator.scheduling);function f(E){for(var D=n(m);D!==null;){if(D.callback===null)r(m);else if(D.startTime<=E)r(m),D.sortIndex=D.expirationTime,t(u,D);else break;D=n(m)}}function h(E){if(p=!1,f(E),!j)if(n(u)!==null)j=!0,q(N);else{var D=n(m);D!==null&&Ln(h,D.startTime-E)}}function N(E,D){j=!1,p&&(p=!1,c(b),b=-1),w=!0;var P=v;try{for(f(D),y=n(u);y!==null&&(!(y.expirationTime>D)||E&&!je());){var G=y.callback;if(typeof G=="function"){y.callback=null,v=y.priorityLevel;var ee=G(y.expirationTime<=D);D=e.unstable_now(),typeof ee=="function"?y.callback=ee:y===n(u)&&r(u),f(D)}else r(u);y=n(u)}if(y!==null)var vr=!0;else{var Mt=n(m);Mt!==null&&Ln(h,Mt.startTime-D),vr=!1}return vr}finally{y=null,v=P,w=!1}}var C=!1,S=null,b=-1,A=5,M=-1;function je(){return!(e.unstable_now()-M<A)}function he(){if(S!==null){var E=e.unstable_now();M=E;var D=!0;try{D=S(!0,E)}finally{D?Tt():(C=!1,S=null)}}else C=!1}var Tt;if(typeof d=="function")Tt=function(){d(he)};else if(typeof MessageChannel<"u"){var Ie=new MessageChannel,Ge=Ie.port2;Ie.port1.onmessage=he,Tt=function(){Ge.postMessage(null)}}else Tt=function(){g(he,0)};function q(E){S=E,C||(C=!0,Tt())}function Ln(E,D){b=g(function(){E(e.unstable_now())},D)}e.unstable_IdlePriority=5,e.unstable_ImmediatePriority=1,e.unstable_LowPriority=4,e.unstable_NormalPriority=3,e.unstable_Profiling=null,e.unstable_UserBlockingPriority=2,e.unstable_cancelCallback=function(E){E.callback=null},e.unstable_continueExecution=function(){j||w||(j=!0,q(N))},e.unstable_forceFrameRate=function(E){0>E||125<E?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):A=0<E?Math.floor(1e3/E):5},e.unstable_getCurrentPriorityLevel=function(){return v},e.unstable_getFirstCallbackNode=function(){return n(u)},e.unstable_next=function(E){switch(v){case 1:case 2:case 3:var D=3;break;default:D=v}var P=v;v=D;try{return E()}finally{v=P}},e.unstable_pauseExecution=function(){},e.unstable_requestPaint=function(){},e.unstable_runWithPriority=function(E,D){switch(E){case 1:case 2:case 3:case 4:case 5:break;default:E=3}var P=v;v=E;try{return D()}finally{v=P}},e.unstable_scheduleCallback=function(E,D,P){var G=e.unstable_now();switch(typeof P=="object"&&P!==null?(P=P.delay,P=typeof P=="number"&&0<P?G+P:G):P=G,E){case 1:var ee=-1;break;case 2:ee=250;break;case 5:ee=**********;break;case 4:ee=1e4;break;default:ee=5e3}return ee=P+ee,E={id:x++,callback:D,priorityLevel:E,startTime:P,expirationTime:ee,sortIndex:-1},P>G?(E.sortIndex=P,t(m,E),n(u)===null&&E===n(m)&&(p?(c(b),b=-1):p=!0,Ln(h,P-G))):(E.sortIndex=ee,t(u,E),j||w||(j=!0,q(N))),E},e.unstable_shouldYield=je,e.unstable_wrapCallback=function(E){var D=v;return function(){var P=v;v=D;try{return E.apply(this,arguments)}finally{v=P}}}})(Ua);Oa.exports=Ua;var gd=Oa.exports;/**
 * @license React
 * react-dom.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var xd=T,Ce=gd;function k(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,n=1;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var Fa=new Set,Xn={};function Kt(e,t){gn(e,t),gn(e+"Capture",t)}function gn(e,t){for(Xn[e]=t,e=0;e<t.length;e++)Fa.add(t[e])}var tt=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),gl=Object.prototype.hasOwnProperty,yd=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,po={},ho={};function vd(e){return gl.call(ho,e)?!0:gl.call(po,e)?!1:yd.test(e)?ho[e]=!0:(po[e]=!0,!1)}function wd(e,t,n,r){if(n!==null&&n.type===0)return!1;switch(typeof t){case"function":case"symbol":return!0;case"boolean":return r?!1:n!==null?!n.acceptsBooleans:(e=e.toLowerCase().slice(0,5),e!=="data-"&&e!=="aria-");default:return!1}}function jd(e,t,n,r){if(t===null||typeof t>"u"||wd(e,t,n,r))return!0;if(r)return!1;if(n!==null)switch(n.type){case 3:return!t;case 4:return t===!1;case 5:return isNaN(t);case 6:return isNaN(t)||1>t}return!1}function pe(e,t,n,r,s,i,o){this.acceptsBooleans=t===2||t===3||t===4,this.attributeName=r,this.attributeNamespace=s,this.mustUseProperty=n,this.propertyName=e,this.type=t,this.sanitizeURL=i,this.removeEmptyString=o}var ie={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach(function(e){ie[e]=new pe(e,0,!1,e,null,!1,!1)});[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach(function(e){var t=e[0];ie[t]=new pe(t,1,!1,e[1],null,!1,!1)});["contentEditable","draggable","spellCheck","value"].forEach(function(e){ie[e]=new pe(e,2,!1,e.toLowerCase(),null,!1,!1)});["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach(function(e){ie[e]=new pe(e,2,!1,e,null,!1,!1)});"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach(function(e){ie[e]=new pe(e,3,!1,e.toLowerCase(),null,!1,!1)});["checked","multiple","muted","selected"].forEach(function(e){ie[e]=new pe(e,3,!0,e,null,!1,!1)});["capture","download"].forEach(function(e){ie[e]=new pe(e,4,!1,e,null,!1,!1)});["cols","rows","size","span"].forEach(function(e){ie[e]=new pe(e,6,!1,e,null,!1,!1)});["rowSpan","start"].forEach(function(e){ie[e]=new pe(e,5,!1,e.toLowerCase(),null,!1,!1)});var vi=/[\-:]([a-z])/g;function wi(e){return e[1].toUpperCase()}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach(function(e){var t=e.replace(vi,wi);ie[t]=new pe(t,1,!1,e,null,!1,!1)});"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach(function(e){var t=e.replace(vi,wi);ie[t]=new pe(t,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)});["xml:base","xml:lang","xml:space"].forEach(function(e){var t=e.replace(vi,wi);ie[t]=new pe(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)});["tabIndex","crossOrigin"].forEach(function(e){ie[e]=new pe(e,1,!1,e.toLowerCase(),null,!1,!1)});ie.xlinkHref=new pe("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1);["src","href","action","formAction"].forEach(function(e){ie[e]=new pe(e,1,!1,e.toLowerCase(),null,!0,!0)});function ji(e,t,n,r){var s=ie.hasOwnProperty(t)?ie[t]:null;(s!==null?s.type!==0:r||!(2<t.length)||t[0]!=="o"&&t[0]!=="O"||t[1]!=="n"&&t[1]!=="N")&&(jd(t,n,s,r)&&(n=null),r||s===null?vd(t)&&(n===null?e.removeAttribute(t):e.setAttribute(t,""+n)):s.mustUseProperty?e[s.propertyName]=n===null?s.type===3?!1:"":n:(t=s.attributeName,r=s.attributeNamespace,n===null?e.removeAttribute(t):(s=s.type,n=s===3||s===4&&n===!0?"":""+n,r?e.setAttributeNS(r,t,n):e.setAttribute(t,n))))}var lt=xd.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,Nr=Symbol.for("react.element"),qt=Symbol.for("react.portal"),Zt=Symbol.for("react.fragment"),Ni=Symbol.for("react.strict_mode"),xl=Symbol.for("react.profiler"),Va=Symbol.for("react.provider"),Ha=Symbol.for("react.context"),Si=Symbol.for("react.forward_ref"),yl=Symbol.for("react.suspense"),vl=Symbol.for("react.suspense_list"),ki=Symbol.for("react.memo"),ot=Symbol.for("react.lazy"),Ba=Symbol.for("react.offscreen"),go=Symbol.iterator;function Tn(e){return e===null||typeof e!="object"?null:(e=go&&e[go]||e["@@iterator"],typeof e=="function"?e:null)}var Q=Object.assign,Hs;function Rn(e){if(Hs===void 0)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);Hs=t&&t[1]||""}return`
`+Hs+e}var Bs=!1;function Ws(e,t){if(!e||Bs)return"";Bs=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(t)if(t=function(){throw Error()},Object.defineProperty(t.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(t,[])}catch(m){var r=m}Reflect.construct(e,[],t)}else{try{t.call()}catch(m){r=m}e.call(t.prototype)}else{try{throw Error()}catch(m){r=m}e()}}catch(m){if(m&&r&&typeof m.stack=="string"){for(var s=m.stack.split(`
`),i=r.stack.split(`
`),o=s.length-1,a=i.length-1;1<=o&&0<=a&&s[o]!==i[a];)a--;for(;1<=o&&0<=a;o--,a--)if(s[o]!==i[a]){if(o!==1||a!==1)do if(o--,a--,0>a||s[o]!==i[a]){var u=`
`+s[o].replace(" at new "," at ");return e.displayName&&u.includes("<anonymous>")&&(u=u.replace("<anonymous>",e.displayName)),u}while(1<=o&&0<=a);break}}}finally{Bs=!1,Error.prepareStackTrace=n}return(e=e?e.displayName||e.name:"")?Rn(e):""}function Nd(e){switch(e.tag){case 5:return Rn(e.type);case 16:return Rn("Lazy");case 13:return Rn("Suspense");case 19:return Rn("SuspenseList");case 0:case 2:case 15:return e=Ws(e.type,!1),e;case 11:return e=Ws(e.type.render,!1),e;case 1:return e=Ws(e.type,!0),e;default:return""}}function wl(e){if(e==null)return null;if(typeof e=="function")return e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case Zt:return"Fragment";case qt:return"Portal";case xl:return"Profiler";case Ni:return"StrictMode";case yl:return"Suspense";case vl:return"SuspenseList"}if(typeof e=="object")switch(e.$$typeof){case Ha:return(e.displayName||"Context")+".Consumer";case Va:return(e._context.displayName||"Context")+".Provider";case Si:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case ki:return t=e.displayName||null,t!==null?t:wl(e.type)||"Memo";case ot:t=e._payload,e=e._init;try{return wl(e(t))}catch{}}return null}function Sd(e){var t=e.type;switch(e.tag){case 24:return"Cache";case 9:return(t.displayName||"Context")+".Consumer";case 10:return(t._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return e=t.render,e=e.displayName||e.name||"",t.displayName||(e!==""?"ForwardRef("+e+")":"ForwardRef");case 7:return"Fragment";case 5:return t;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return wl(t);case 8:return t===Ni?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if(typeof t=="function")return t.displayName||t.name||null;if(typeof t=="string")return t}return null}function St(e){switch(typeof e){case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function Wa(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function kd(e){var t=Wa(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),r=""+e[t];if(!e.hasOwnProperty(t)&&typeof n<"u"&&typeof n.get=="function"&&typeof n.set=="function"){var s=n.get,i=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return s.call(this)},set:function(o){r=""+o,i.call(this,o)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return r},setValue:function(o){r=""+o},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function Sr(e){e._valueTracker||(e._valueTracker=kd(e))}function Qa(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),r="";return e&&(r=Wa(e)?e.checked?"true":"false":e.value),e=r,e!==n?(t.setValue(e),!0):!1}function Zr(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}function jl(e,t){var n=t.checked;return Q({},t,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:n??e._wrapperState.initialChecked})}function xo(e,t){var n=t.defaultValue==null?"":t.defaultValue,r=t.checked!=null?t.checked:t.defaultChecked;n=St(t.value!=null?t.value:n),e._wrapperState={initialChecked:r,initialValue:n,controlled:t.type==="checkbox"||t.type==="radio"?t.checked!=null:t.value!=null}}function Ka(e,t){t=t.checked,t!=null&&ji(e,"checked",t,!1)}function Nl(e,t){Ka(e,t);var n=St(t.value),r=t.type;if(n!=null)r==="number"?(n===0&&e.value===""||e.value!=n)&&(e.value=""+n):e.value!==""+n&&(e.value=""+n);else if(r==="submit"||r==="reset"){e.removeAttribute("value");return}t.hasOwnProperty("value")?Sl(e,t.type,n):t.hasOwnProperty("defaultValue")&&Sl(e,t.type,St(t.defaultValue)),t.checked==null&&t.defaultChecked!=null&&(e.defaultChecked=!!t.defaultChecked)}function yo(e,t,n){if(t.hasOwnProperty("value")||t.hasOwnProperty("defaultValue")){var r=t.type;if(!(r!=="submit"&&r!=="reset"||t.value!==void 0&&t.value!==null))return;t=""+e._wrapperState.initialValue,n||t===e.value||(e.value=t),e.defaultValue=t}n=e.name,n!==""&&(e.name=""),e.defaultChecked=!!e._wrapperState.initialChecked,n!==""&&(e.name=n)}function Sl(e,t,n){(t!=="number"||Zr(e.ownerDocument)!==e)&&(n==null?e.defaultValue=""+e._wrapperState.initialValue:e.defaultValue!==""+n&&(e.defaultValue=""+n))}var $n=Array.isArray;function cn(e,t,n,r){if(e=e.options,t){t={};for(var s=0;s<n.length;s++)t["$"+n[s]]=!0;for(n=0;n<e.length;n++)s=t.hasOwnProperty("$"+e[n].value),e[n].selected!==s&&(e[n].selected=s),s&&r&&(e[n].defaultSelected=!0)}else{for(n=""+St(n),t=null,s=0;s<e.length;s++){if(e[s].value===n){e[s].selected=!0,r&&(e[s].defaultSelected=!0);return}t!==null||e[s].disabled||(t=e[s])}t!==null&&(t.selected=!0)}}function kl(e,t){if(t.dangerouslySetInnerHTML!=null)throw Error(k(91));return Q({},t,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})}function vo(e,t){var n=t.value;if(n==null){if(n=t.children,t=t.defaultValue,n!=null){if(t!=null)throw Error(k(92));if($n(n)){if(1<n.length)throw Error(k(93));n=n[0]}t=n}t==null&&(t=""),n=t}e._wrapperState={initialValue:St(n)}}function Ga(e,t){var n=St(t.value),r=St(t.defaultValue);n!=null&&(n=""+n,n!==e.value&&(e.value=n),t.defaultValue==null&&e.defaultValue!==n&&(e.defaultValue=n)),r!=null&&(e.defaultValue=""+r)}function wo(e){var t=e.textContent;t===e._wrapperState.initialValue&&t!==""&&t!==null&&(e.value=t)}function Ya(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function Cl(e,t){return e==null||e==="http://www.w3.org/1999/xhtml"?Ya(t):e==="http://www.w3.org/2000/svg"&&t==="foreignObject"?"http://www.w3.org/1999/xhtml":e}var kr,Xa=function(e){return typeof MSApp<"u"&&MSApp.execUnsafeLocalFunction?function(t,n,r,s){MSApp.execUnsafeLocalFunction(function(){return e(t,n,r,s)})}:e}(function(e,t){if(e.namespaceURI!=="http://www.w3.org/2000/svg"||"innerHTML"in e)e.innerHTML=t;else{for(kr=kr||document.createElement("div"),kr.innerHTML="<svg>"+t.valueOf().toString()+"</svg>",t=kr.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;t.firstChild;)e.appendChild(t.firstChild)}});function qn(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&n.nodeType===3){n.nodeValue=t;return}}e.textContent=t}var Fn={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},Cd=["Webkit","ms","Moz","O"];Object.keys(Fn).forEach(function(e){Cd.forEach(function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),Fn[t]=Fn[e]})});function qa(e,t,n){return t==null||typeof t=="boolean"||t===""?"":n||typeof t!="number"||t===0||Fn.hasOwnProperty(e)&&Fn[e]?(""+t).trim():t+"px"}function Za(e,t){e=e.style;for(var n in t)if(t.hasOwnProperty(n)){var r=n.indexOf("--")===0,s=qa(n,t[n],r);n==="float"&&(n="cssFloat"),r?e.setProperty(n,s):e[n]=s}}var bd=Q({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function bl(e,t){if(t){if(bd[e]&&(t.children!=null||t.dangerouslySetInnerHTML!=null))throw Error(k(137,e));if(t.dangerouslySetInnerHTML!=null){if(t.children!=null)throw Error(k(60));if(typeof t.dangerouslySetInnerHTML!="object"||!("__html"in t.dangerouslySetInnerHTML))throw Error(k(61))}if(t.style!=null&&typeof t.style!="object")throw Error(k(62))}}function El(e,t){if(e.indexOf("-")===-1)return typeof t.is=="string";switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var Ll=null;function Ci(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var Tl=null,dn=null,fn=null;function jo(e){if(e=xr(e)){if(typeof Tl!="function")throw Error(k(280));var t=e.stateNode;t&&(t=Ms(t),Tl(e.stateNode,e.type,t))}}function Ja(e){dn?fn?fn.push(e):fn=[e]:dn=e}function eu(){if(dn){var e=dn,t=fn;if(fn=dn=null,jo(e),t)for(e=0;e<t.length;e++)jo(t[e])}}function tu(e,t){return e(t)}function nu(){}var Qs=!1;function ru(e,t,n){if(Qs)return e(t,n);Qs=!0;try{return tu(e,t,n)}finally{Qs=!1,(dn!==null||fn!==null)&&(nu(),eu())}}function Zn(e,t){var n=e.stateNode;if(n===null)return null;var r=Ms(n);if(r===null)return null;n=r[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(r=!r.disabled)||(e=e.type,r=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!r;break e;default:e=!1}if(e)return null;if(n&&typeof n!="function")throw Error(k(231,t,typeof n));return n}var Ml=!1;if(tt)try{var Mn={};Object.defineProperty(Mn,"passive",{get:function(){Ml=!0}}),window.addEventListener("test",Mn,Mn),window.removeEventListener("test",Mn,Mn)}catch{Ml=!1}function Ed(e,t,n,r,s,i,o,a,u){var m=Array.prototype.slice.call(arguments,3);try{t.apply(n,m)}catch(x){this.onError(x)}}var Vn=!1,Jr=null,es=!1,Dl=null,Ld={onError:function(e){Vn=!0,Jr=e}};function Td(e,t,n,r,s,i,o,a,u){Vn=!1,Jr=null,Ed.apply(Ld,arguments)}function Md(e,t,n,r,s,i,o,a,u){if(Td.apply(this,arguments),Vn){if(Vn){var m=Jr;Vn=!1,Jr=null}else throw Error(k(198));es||(es=!0,Dl=m)}}function Gt(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,t.flags&4098&&(n=t.return),e=t.return;while(e)}return t.tag===3?n:null}function su(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function No(e){if(Gt(e)!==e)throw Error(k(188))}function Dd(e){var t=e.alternate;if(!t){if(t=Gt(e),t===null)throw Error(k(188));return t!==e?null:e}for(var n=e,r=t;;){var s=n.return;if(s===null)break;var i=s.alternate;if(i===null){if(r=s.return,r!==null){n=r;continue}break}if(s.child===i.child){for(i=s.child;i;){if(i===n)return No(s),e;if(i===r)return No(s),t;i=i.sibling}throw Error(k(188))}if(n.return!==r.return)n=s,r=i;else{for(var o=!1,a=s.child;a;){if(a===n){o=!0,n=s,r=i;break}if(a===r){o=!0,r=s,n=i;break}a=a.sibling}if(!o){for(a=i.child;a;){if(a===n){o=!0,n=i,r=s;break}if(a===r){o=!0,r=i,n=s;break}a=a.sibling}if(!o)throw Error(k(189))}}if(n.alternate!==r)throw Error(k(190))}if(n.tag!==3)throw Error(k(188));return n.stateNode.current===n?e:t}function lu(e){return e=Dd(e),e!==null?iu(e):null}function iu(e){if(e.tag===5||e.tag===6)return e;for(e=e.child;e!==null;){var t=iu(e);if(t!==null)return t;e=e.sibling}return null}var ou=Ce.unstable_scheduleCallback,So=Ce.unstable_cancelCallback,_d=Ce.unstable_shouldYield,Ad=Ce.unstable_requestPaint,Y=Ce.unstable_now,Pd=Ce.unstable_getCurrentPriorityLevel,bi=Ce.unstable_ImmediatePriority,au=Ce.unstable_UserBlockingPriority,ts=Ce.unstable_NormalPriority,Id=Ce.unstable_LowPriority,uu=Ce.unstable_IdlePriority,bs=null,Qe=null;function zd(e){if(Qe&&typeof Qe.onCommitFiberRoot=="function")try{Qe.onCommitFiberRoot(bs,e,void 0,(e.current.flags&128)===128)}catch{}}var Ue=Math.clz32?Math.clz32:Od,Rd=Math.log,$d=Math.LN2;function Od(e){return e>>>=0,e===0?32:31-(Rd(e)/$d|0)|0}var Cr=64,br=4194304;function On(e){switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194240;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return e&130023424;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return e}}function ns(e,t){var n=e.pendingLanes;if(n===0)return 0;var r=0,s=e.suspendedLanes,i=e.pingedLanes,o=n&268435455;if(o!==0){var a=o&~s;a!==0?r=On(a):(i&=o,i!==0&&(r=On(i)))}else o=n&~s,o!==0?r=On(o):i!==0&&(r=On(i));if(r===0)return 0;if(t!==0&&t!==r&&!(t&s)&&(s=r&-r,i=t&-t,s>=i||s===16&&(i&4194240)!==0))return t;if(r&4&&(r|=n&16),t=e.entangledLanes,t!==0)for(e=e.entanglements,t&=r;0<t;)n=31-Ue(t),s=1<<n,r|=e[n],t&=~s;return r}function Ud(e,t){switch(e){case 1:case 2:case 4:return t+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return-1;case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function Fd(e,t){for(var n=e.suspendedLanes,r=e.pingedLanes,s=e.expirationTimes,i=e.pendingLanes;0<i;){var o=31-Ue(i),a=1<<o,u=s[o];u===-1?(!(a&n)||a&r)&&(s[o]=Ud(a,t)):u<=t&&(e.expiredLanes|=a),i&=~a}}function _l(e){return e=e.pendingLanes&-1073741825,e!==0?e:e&1073741824?1073741824:0}function cu(){var e=Cr;return Cr<<=1,!(Cr&4194240)&&(Cr=64),e}function Ks(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function hr(e,t,n){e.pendingLanes|=t,t!==536870912&&(e.suspendedLanes=0,e.pingedLanes=0),e=e.eventTimes,t=31-Ue(t),e[t]=n}function Vd(e,t){var n=e.pendingLanes&~t;e.pendingLanes=t,e.suspendedLanes=0,e.pingedLanes=0,e.expiredLanes&=t,e.mutableReadLanes&=t,e.entangledLanes&=t,t=e.entanglements;var r=e.eventTimes;for(e=e.expirationTimes;0<n;){var s=31-Ue(n),i=1<<s;t[s]=0,r[s]=-1,e[s]=-1,n&=~i}}function Ei(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var r=31-Ue(n),s=1<<r;s&t|e[r]&t&&(e[r]|=t),n&=~s}}var $=0;function du(e){return e&=-e,1<e?4<e?e&268435455?16:536870912:4:1}var fu,Li,mu,pu,hu,Al=!1,Er=[],mt=null,pt=null,ht=null,Jn=new Map,er=new Map,ut=[],Hd="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function ko(e,t){switch(e){case"focusin":case"focusout":mt=null;break;case"dragenter":case"dragleave":pt=null;break;case"mouseover":case"mouseout":ht=null;break;case"pointerover":case"pointerout":Jn.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":er.delete(t.pointerId)}}function Dn(e,t,n,r,s,i){return e===null||e.nativeEvent!==i?(e={blockedOn:t,domEventName:n,eventSystemFlags:r,nativeEvent:i,targetContainers:[s]},t!==null&&(t=xr(t),t!==null&&Li(t)),e):(e.eventSystemFlags|=r,t=e.targetContainers,s!==null&&t.indexOf(s)===-1&&t.push(s),e)}function Bd(e,t,n,r,s){switch(t){case"focusin":return mt=Dn(mt,e,t,n,r,s),!0;case"dragenter":return pt=Dn(pt,e,t,n,r,s),!0;case"mouseover":return ht=Dn(ht,e,t,n,r,s),!0;case"pointerover":var i=s.pointerId;return Jn.set(i,Dn(Jn.get(i)||null,e,t,n,r,s)),!0;case"gotpointercapture":return i=s.pointerId,er.set(i,Dn(er.get(i)||null,e,t,n,r,s)),!0}return!1}function gu(e){var t=zt(e.target);if(t!==null){var n=Gt(t);if(n!==null){if(t=n.tag,t===13){if(t=su(n),t!==null){e.blockedOn=t,hu(e.priority,function(){mu(n)});return}}else if(t===3&&n.stateNode.current.memoizedState.isDehydrated){e.blockedOn=n.tag===3?n.stateNode.containerInfo:null;return}}}e.blockedOn=null}function Fr(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var n=Pl(e.domEventName,e.eventSystemFlags,t[0],e.nativeEvent);if(n===null){n=e.nativeEvent;var r=new n.constructor(n.type,n);Ll=r,n.target.dispatchEvent(r),Ll=null}else return t=xr(n),t!==null&&Li(t),e.blockedOn=n,!1;t.shift()}return!0}function Co(e,t,n){Fr(e)&&n.delete(t)}function Wd(){Al=!1,mt!==null&&Fr(mt)&&(mt=null),pt!==null&&Fr(pt)&&(pt=null),ht!==null&&Fr(ht)&&(ht=null),Jn.forEach(Co),er.forEach(Co)}function _n(e,t){e.blockedOn===t&&(e.blockedOn=null,Al||(Al=!0,Ce.unstable_scheduleCallback(Ce.unstable_NormalPriority,Wd)))}function tr(e){function t(s){return _n(s,e)}if(0<Er.length){_n(Er[0],e);for(var n=1;n<Er.length;n++){var r=Er[n];r.blockedOn===e&&(r.blockedOn=null)}}for(mt!==null&&_n(mt,e),pt!==null&&_n(pt,e),ht!==null&&_n(ht,e),Jn.forEach(t),er.forEach(t),n=0;n<ut.length;n++)r=ut[n],r.blockedOn===e&&(r.blockedOn=null);for(;0<ut.length&&(n=ut[0],n.blockedOn===null);)gu(n),n.blockedOn===null&&ut.shift()}var mn=lt.ReactCurrentBatchConfig,rs=!0;function Qd(e,t,n,r){var s=$,i=mn.transition;mn.transition=null;try{$=1,Ti(e,t,n,r)}finally{$=s,mn.transition=i}}function Kd(e,t,n,r){var s=$,i=mn.transition;mn.transition=null;try{$=4,Ti(e,t,n,r)}finally{$=s,mn.transition=i}}function Ti(e,t,n,r){if(rs){var s=Pl(e,t,n,r);if(s===null)rl(e,t,r,ss,n),ko(e,r);else if(Bd(s,e,t,n,r))r.stopPropagation();else if(ko(e,r),t&4&&-1<Hd.indexOf(e)){for(;s!==null;){var i=xr(s);if(i!==null&&fu(i),i=Pl(e,t,n,r),i===null&&rl(e,t,r,ss,n),i===s)break;s=i}s!==null&&r.stopPropagation()}else rl(e,t,r,null,n)}}var ss=null;function Pl(e,t,n,r){if(ss=null,e=Ci(r),e=zt(e),e!==null)if(t=Gt(e),t===null)e=null;else if(n=t.tag,n===13){if(e=su(t),e!==null)return e;e=null}else if(n===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null);return ss=e,null}function xu(e){switch(e){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(Pd()){case bi:return 1;case au:return 4;case ts:case Id:return 16;case uu:return 536870912;default:return 16}default:return 16}}var dt=null,Mi=null,Vr=null;function yu(){if(Vr)return Vr;var e,t=Mi,n=t.length,r,s="value"in dt?dt.value:dt.textContent,i=s.length;for(e=0;e<n&&t[e]===s[e];e++);var o=n-e;for(r=1;r<=o&&t[n-r]===s[i-r];r++);return Vr=s.slice(e,1<r?1-r:void 0)}function Hr(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function Lr(){return!0}function bo(){return!1}function Ee(e){function t(n,r,s,i,o){this._reactName=n,this._targetInst=s,this.type=r,this.nativeEvent=i,this.target=o,this.currentTarget=null;for(var a in e)e.hasOwnProperty(a)&&(n=e[a],this[a]=n?n(i):i[a]);return this.isDefaultPrevented=(i.defaultPrevented!=null?i.defaultPrevented:i.returnValue===!1)?Lr:bo,this.isPropagationStopped=bo,this}return Q(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var n=this.nativeEvent;n&&(n.preventDefault?n.preventDefault():typeof n.returnValue!="unknown"&&(n.returnValue=!1),this.isDefaultPrevented=Lr)},stopPropagation:function(){var n=this.nativeEvent;n&&(n.stopPropagation?n.stopPropagation():typeof n.cancelBubble!="unknown"&&(n.cancelBubble=!0),this.isPropagationStopped=Lr)},persist:function(){},isPersistent:Lr}),t}var bn={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},Di=Ee(bn),gr=Q({},bn,{view:0,detail:0}),Gd=Ee(gr),Gs,Ys,An,Es=Q({},gr,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:_i,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==An&&(An&&e.type==="mousemove"?(Gs=e.screenX-An.screenX,Ys=e.screenY-An.screenY):Ys=Gs=0,An=e),Gs)},movementY:function(e){return"movementY"in e?e.movementY:Ys}}),Eo=Ee(Es),Yd=Q({},Es,{dataTransfer:0}),Xd=Ee(Yd),qd=Q({},gr,{relatedTarget:0}),Xs=Ee(qd),Zd=Q({},bn,{animationName:0,elapsedTime:0,pseudoElement:0}),Jd=Ee(Zd),ef=Q({},bn,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),tf=Ee(ef),nf=Q({},bn,{data:0}),Lo=Ee(nf),rf={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},sf={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},lf={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function of(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=lf[e])?!!t[e]:!1}function _i(){return of}var af=Q({},gr,{key:function(e){if(e.key){var t=rf[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=Hr(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?sf[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:_i,charCode:function(e){return e.type==="keypress"?Hr(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?Hr(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),uf=Ee(af),cf=Q({},Es,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),To=Ee(cf),df=Q({},gr,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:_i}),ff=Ee(df),mf=Q({},bn,{propertyName:0,elapsedTime:0,pseudoElement:0}),pf=Ee(mf),hf=Q({},Es,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),gf=Ee(hf),xf=[9,13,27,32],Ai=tt&&"CompositionEvent"in window,Hn=null;tt&&"documentMode"in document&&(Hn=document.documentMode);var yf=tt&&"TextEvent"in window&&!Hn,vu=tt&&(!Ai||Hn&&8<Hn&&11>=Hn),Mo=String.fromCharCode(32),Do=!1;function wu(e,t){switch(e){case"keyup":return xf.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function ju(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var Jt=!1;function vf(e,t){switch(e){case"compositionend":return ju(t);case"keypress":return t.which!==32?null:(Do=!0,Mo);case"textInput":return e=t.data,e===Mo&&Do?null:e;default:return null}}function wf(e,t){if(Jt)return e==="compositionend"||!Ai&&wu(e,t)?(e=yu(),Vr=Mi=dt=null,Jt=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return vu&&t.locale!=="ko"?null:t.data;default:return null}}var jf={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function _o(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!jf[e.type]:t==="textarea"}function Nu(e,t,n,r){Ja(r),t=ls(t,"onChange"),0<t.length&&(n=new Di("onChange","change",null,n,r),e.push({event:n,listeners:t}))}var Bn=null,nr=null;function Nf(e){Au(e,0)}function Ls(e){var t=nn(e);if(Qa(t))return e}function Sf(e,t){if(e==="change")return t}var Su=!1;if(tt){var qs;if(tt){var Zs="oninput"in document;if(!Zs){var Ao=document.createElement("div");Ao.setAttribute("oninput","return;"),Zs=typeof Ao.oninput=="function"}qs=Zs}else qs=!1;Su=qs&&(!document.documentMode||9<document.documentMode)}function Po(){Bn&&(Bn.detachEvent("onpropertychange",ku),nr=Bn=null)}function ku(e){if(e.propertyName==="value"&&Ls(nr)){var t=[];Nu(t,nr,e,Ci(e)),ru(Nf,t)}}function kf(e,t,n){e==="focusin"?(Po(),Bn=t,nr=n,Bn.attachEvent("onpropertychange",ku)):e==="focusout"&&Po()}function Cf(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return Ls(nr)}function bf(e,t){if(e==="click")return Ls(t)}function Ef(e,t){if(e==="input"||e==="change")return Ls(t)}function Lf(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var Ve=typeof Object.is=="function"?Object.is:Lf;function rr(e,t){if(Ve(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(r=0;r<n.length;r++){var s=n[r];if(!gl.call(t,s)||!Ve(e[s],t[s]))return!1}return!0}function Io(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function zo(e,t){var n=Io(e);e=0;for(var r;n;){if(n.nodeType===3){if(r=e+n.textContent.length,e<=t&&r>=t)return{node:n,offset:t-e};e=r}e:{for(;n;){if(n.nextSibling){n=n.nextSibling;break e}n=n.parentNode}n=void 0}n=Io(n)}}function Cu(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?Cu(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function bu(){for(var e=window,t=Zr();t instanceof e.HTMLIFrameElement;){try{var n=typeof t.contentWindow.location.href=="string"}catch{n=!1}if(n)e=t.contentWindow;else break;t=Zr(e.document)}return t}function Pi(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}function Tf(e){var t=bu(),n=e.focusedElem,r=e.selectionRange;if(t!==n&&n&&n.ownerDocument&&Cu(n.ownerDocument.documentElement,n)){if(r!==null&&Pi(n)){if(t=r.start,e=r.end,e===void 0&&(e=t),"selectionStart"in n)n.selectionStart=t,n.selectionEnd=Math.min(e,n.value.length);else if(e=(t=n.ownerDocument||document)&&t.defaultView||window,e.getSelection){e=e.getSelection();var s=n.textContent.length,i=Math.min(r.start,s);r=r.end===void 0?i:Math.min(r.end,s),!e.extend&&i>r&&(s=r,r=i,i=s),s=zo(n,i);var o=zo(n,r);s&&o&&(e.rangeCount!==1||e.anchorNode!==s.node||e.anchorOffset!==s.offset||e.focusNode!==o.node||e.focusOffset!==o.offset)&&(t=t.createRange(),t.setStart(s.node,s.offset),e.removeAllRanges(),i>r?(e.addRange(t),e.extend(o.node,o.offset)):(t.setEnd(o.node,o.offset),e.addRange(t)))}}for(t=[],e=n;e=e.parentNode;)e.nodeType===1&&t.push({element:e,left:e.scrollLeft,top:e.scrollTop});for(typeof n.focus=="function"&&n.focus(),n=0;n<t.length;n++)e=t[n],e.element.scrollLeft=e.left,e.element.scrollTop=e.top}}var Mf=tt&&"documentMode"in document&&11>=document.documentMode,en=null,Il=null,Wn=null,zl=!1;function Ro(e,t,n){var r=n.window===n?n.document:n.nodeType===9?n:n.ownerDocument;zl||en==null||en!==Zr(r)||(r=en,"selectionStart"in r&&Pi(r)?r={start:r.selectionStart,end:r.selectionEnd}:(r=(r.ownerDocument&&r.ownerDocument.defaultView||window).getSelection(),r={anchorNode:r.anchorNode,anchorOffset:r.anchorOffset,focusNode:r.focusNode,focusOffset:r.focusOffset}),Wn&&rr(Wn,r)||(Wn=r,r=ls(Il,"onSelect"),0<r.length&&(t=new Di("onSelect","select",null,t,n),e.push({event:t,listeners:r}),t.target=en)))}function Tr(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var tn={animationend:Tr("Animation","AnimationEnd"),animationiteration:Tr("Animation","AnimationIteration"),animationstart:Tr("Animation","AnimationStart"),transitionend:Tr("Transition","TransitionEnd")},Js={},Eu={};tt&&(Eu=document.createElement("div").style,"AnimationEvent"in window||(delete tn.animationend.animation,delete tn.animationiteration.animation,delete tn.animationstart.animation),"TransitionEvent"in window||delete tn.transitionend.transition);function Ts(e){if(Js[e])return Js[e];if(!tn[e])return e;var t=tn[e],n;for(n in t)if(t.hasOwnProperty(n)&&n in Eu)return Js[e]=t[n];return e}var Lu=Ts("animationend"),Tu=Ts("animationiteration"),Mu=Ts("animationstart"),Du=Ts("transitionend"),_u=new Map,$o="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function Ct(e,t){_u.set(e,t),Kt(t,[e])}for(var el=0;el<$o.length;el++){var tl=$o[el],Df=tl.toLowerCase(),_f=tl[0].toUpperCase()+tl.slice(1);Ct(Df,"on"+_f)}Ct(Lu,"onAnimationEnd");Ct(Tu,"onAnimationIteration");Ct(Mu,"onAnimationStart");Ct("dblclick","onDoubleClick");Ct("focusin","onFocus");Ct("focusout","onBlur");Ct(Du,"onTransitionEnd");gn("onMouseEnter",["mouseout","mouseover"]);gn("onMouseLeave",["mouseout","mouseover"]);gn("onPointerEnter",["pointerout","pointerover"]);gn("onPointerLeave",["pointerout","pointerover"]);Kt("onChange","change click focusin focusout input keydown keyup selectionchange".split(" "));Kt("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" "));Kt("onBeforeInput",["compositionend","keypress","textInput","paste"]);Kt("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" "));Kt("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" "));Kt("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var Un="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),Af=new Set("cancel close invalid load scroll toggle".split(" ").concat(Un));function Oo(e,t,n){var r=e.type||"unknown-event";e.currentTarget=n,Md(r,t,void 0,e),e.currentTarget=null}function Au(e,t){t=(t&4)!==0;for(var n=0;n<e.length;n++){var r=e[n],s=r.event;r=r.listeners;e:{var i=void 0;if(t)for(var o=r.length-1;0<=o;o--){var a=r[o],u=a.instance,m=a.currentTarget;if(a=a.listener,u!==i&&s.isPropagationStopped())break e;Oo(s,a,m),i=u}else for(o=0;o<r.length;o++){if(a=r[o],u=a.instance,m=a.currentTarget,a=a.listener,u!==i&&s.isPropagationStopped())break e;Oo(s,a,m),i=u}}}if(es)throw e=Dl,es=!1,Dl=null,e}function F(e,t){var n=t[Fl];n===void 0&&(n=t[Fl]=new Set);var r=e+"__bubble";n.has(r)||(Pu(t,e,2,!1),n.add(r))}function nl(e,t,n){var r=0;t&&(r|=4),Pu(n,e,r,t)}var Mr="_reactListening"+Math.random().toString(36).slice(2);function sr(e){if(!e[Mr]){e[Mr]=!0,Fa.forEach(function(n){n!=="selectionchange"&&(Af.has(n)||nl(n,!1,e),nl(n,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[Mr]||(t[Mr]=!0,nl("selectionchange",!1,t))}}function Pu(e,t,n,r){switch(xu(t)){case 1:var s=Qd;break;case 4:s=Kd;break;default:s=Ti}n=s.bind(null,t,n,e),s=void 0,!Ml||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(s=!0),r?s!==void 0?e.addEventListener(t,n,{capture:!0,passive:s}):e.addEventListener(t,n,!0):s!==void 0?e.addEventListener(t,n,{passive:s}):e.addEventListener(t,n,!1)}function rl(e,t,n,r,s){var i=r;if(!(t&1)&&!(t&2)&&r!==null)e:for(;;){if(r===null)return;var o=r.tag;if(o===3||o===4){var a=r.stateNode.containerInfo;if(a===s||a.nodeType===8&&a.parentNode===s)break;if(o===4)for(o=r.return;o!==null;){var u=o.tag;if((u===3||u===4)&&(u=o.stateNode.containerInfo,u===s||u.nodeType===8&&u.parentNode===s))return;o=o.return}for(;a!==null;){if(o=zt(a),o===null)return;if(u=o.tag,u===5||u===6){r=i=o;continue e}a=a.parentNode}}r=r.return}ru(function(){var m=i,x=Ci(n),y=[];e:{var v=_u.get(e);if(v!==void 0){var w=Di,j=e;switch(e){case"keypress":if(Hr(n)===0)break e;case"keydown":case"keyup":w=uf;break;case"focusin":j="focus",w=Xs;break;case"focusout":j="blur",w=Xs;break;case"beforeblur":case"afterblur":w=Xs;break;case"click":if(n.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":w=Eo;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":w=Xd;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":w=ff;break;case Lu:case Tu:case Mu:w=Jd;break;case Du:w=pf;break;case"scroll":w=Gd;break;case"wheel":w=gf;break;case"copy":case"cut":case"paste":w=tf;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":w=To}var p=(t&4)!==0,g=!p&&e==="scroll",c=p?v!==null?v+"Capture":null:v;p=[];for(var d=m,f;d!==null;){f=d;var h=f.stateNode;if(f.tag===5&&h!==null&&(f=h,c!==null&&(h=Zn(d,c),h!=null&&p.push(lr(d,h,f)))),g)break;d=d.return}0<p.length&&(v=new w(v,j,null,n,x),y.push({event:v,listeners:p}))}}if(!(t&7)){e:{if(v=e==="mouseover"||e==="pointerover",w=e==="mouseout"||e==="pointerout",v&&n!==Ll&&(j=n.relatedTarget||n.fromElement)&&(zt(j)||j[nt]))break e;if((w||v)&&(v=x.window===x?x:(v=x.ownerDocument)?v.defaultView||v.parentWindow:window,w?(j=n.relatedTarget||n.toElement,w=m,j=j?zt(j):null,j!==null&&(g=Gt(j),j!==g||j.tag!==5&&j.tag!==6)&&(j=null)):(w=null,j=m),w!==j)){if(p=Eo,h="onMouseLeave",c="onMouseEnter",d="mouse",(e==="pointerout"||e==="pointerover")&&(p=To,h="onPointerLeave",c="onPointerEnter",d="pointer"),g=w==null?v:nn(w),f=j==null?v:nn(j),v=new p(h,d+"leave",w,n,x),v.target=g,v.relatedTarget=f,h=null,zt(x)===m&&(p=new p(c,d+"enter",j,n,x),p.target=f,p.relatedTarget=g,h=p),g=h,w&&j)t:{for(p=w,c=j,d=0,f=p;f;f=Xt(f))d++;for(f=0,h=c;h;h=Xt(h))f++;for(;0<d-f;)p=Xt(p),d--;for(;0<f-d;)c=Xt(c),f--;for(;d--;){if(p===c||c!==null&&p===c.alternate)break t;p=Xt(p),c=Xt(c)}p=null}else p=null;w!==null&&Uo(y,v,w,p,!1),j!==null&&g!==null&&Uo(y,g,j,p,!0)}}e:{if(v=m?nn(m):window,w=v.nodeName&&v.nodeName.toLowerCase(),w==="select"||w==="input"&&v.type==="file")var N=Sf;else if(_o(v))if(Su)N=Ef;else{N=Cf;var C=kf}else(w=v.nodeName)&&w.toLowerCase()==="input"&&(v.type==="checkbox"||v.type==="radio")&&(N=bf);if(N&&(N=N(e,m))){Nu(y,N,n,x);break e}C&&C(e,v,m),e==="focusout"&&(C=v._wrapperState)&&C.controlled&&v.type==="number"&&Sl(v,"number",v.value)}switch(C=m?nn(m):window,e){case"focusin":(_o(C)||C.contentEditable==="true")&&(en=C,Il=m,Wn=null);break;case"focusout":Wn=Il=en=null;break;case"mousedown":zl=!0;break;case"contextmenu":case"mouseup":case"dragend":zl=!1,Ro(y,n,x);break;case"selectionchange":if(Mf)break;case"keydown":case"keyup":Ro(y,n,x)}var S;if(Ai)e:{switch(e){case"compositionstart":var b="onCompositionStart";break e;case"compositionend":b="onCompositionEnd";break e;case"compositionupdate":b="onCompositionUpdate";break e}b=void 0}else Jt?wu(e,n)&&(b="onCompositionEnd"):e==="keydown"&&n.keyCode===229&&(b="onCompositionStart");b&&(vu&&n.locale!=="ko"&&(Jt||b!=="onCompositionStart"?b==="onCompositionEnd"&&Jt&&(S=yu()):(dt=x,Mi="value"in dt?dt.value:dt.textContent,Jt=!0)),C=ls(m,b),0<C.length&&(b=new Lo(b,e,null,n,x),y.push({event:b,listeners:C}),S?b.data=S:(S=ju(n),S!==null&&(b.data=S)))),(S=yf?vf(e,n):wf(e,n))&&(m=ls(m,"onBeforeInput"),0<m.length&&(x=new Lo("onBeforeInput","beforeinput",null,n,x),y.push({event:x,listeners:m}),x.data=S))}Au(y,t)})}function lr(e,t,n){return{instance:e,listener:t,currentTarget:n}}function ls(e,t){for(var n=t+"Capture",r=[];e!==null;){var s=e,i=s.stateNode;s.tag===5&&i!==null&&(s=i,i=Zn(e,n),i!=null&&r.unshift(lr(e,i,s)),i=Zn(e,t),i!=null&&r.push(lr(e,i,s))),e=e.return}return r}function Xt(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5);return e||null}function Uo(e,t,n,r,s){for(var i=t._reactName,o=[];n!==null&&n!==r;){var a=n,u=a.alternate,m=a.stateNode;if(u!==null&&u===r)break;a.tag===5&&m!==null&&(a=m,s?(u=Zn(n,i),u!=null&&o.unshift(lr(n,u,a))):s||(u=Zn(n,i),u!=null&&o.push(lr(n,u,a)))),n=n.return}o.length!==0&&e.push({event:t,listeners:o})}var Pf=/\r\n?/g,If=/\u0000|\uFFFD/g;function Fo(e){return(typeof e=="string"?e:""+e).replace(Pf,`
`).replace(If,"")}function Dr(e,t,n){if(t=Fo(t),Fo(e)!==t&&n)throw Error(k(425))}function is(){}var Rl=null,$l=null;function Ol(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var Ul=typeof setTimeout=="function"?setTimeout:void 0,zf=typeof clearTimeout=="function"?clearTimeout:void 0,Vo=typeof Promise=="function"?Promise:void 0,Rf=typeof queueMicrotask=="function"?queueMicrotask:typeof Vo<"u"?function(e){return Vo.resolve(null).then(e).catch($f)}:Ul;function $f(e){setTimeout(function(){throw e})}function sl(e,t){var n=t,r=0;do{var s=n.nextSibling;if(e.removeChild(n),s&&s.nodeType===8)if(n=s.data,n==="/$"){if(r===0){e.removeChild(s),tr(t);return}r--}else n!=="$"&&n!=="$?"&&n!=="$!"||r++;n=s}while(n);tr(t)}function gt(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?")break;if(t==="/$")return null}}return e}function Ho(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="$"||n==="$!"||n==="$?"){if(t===0)return e;t--}else n==="/$"&&t++}e=e.previousSibling}return null}var En=Math.random().toString(36).slice(2),We="__reactFiber$"+En,ir="__reactProps$"+En,nt="__reactContainer$"+En,Fl="__reactEvents$"+En,Of="__reactListeners$"+En,Uf="__reactHandles$"+En;function zt(e){var t=e[We];if(t)return t;for(var n=e.parentNode;n;){if(t=n[nt]||n[We]){if(n=t.alternate,t.child!==null||n!==null&&n.child!==null)for(e=Ho(e);e!==null;){if(n=e[We])return n;e=Ho(e)}return t}e=n,n=e.parentNode}return null}function xr(e){return e=e[We]||e[nt],!e||e.tag!==5&&e.tag!==6&&e.tag!==13&&e.tag!==3?null:e}function nn(e){if(e.tag===5||e.tag===6)return e.stateNode;throw Error(k(33))}function Ms(e){return e[ir]||null}var Vl=[],rn=-1;function bt(e){return{current:e}}function V(e){0>rn||(e.current=Vl[rn],Vl[rn]=null,rn--)}function U(e,t){rn++,Vl[rn]=e.current,e.current=t}var kt={},ce=bt(kt),ye=bt(!1),Ft=kt;function xn(e,t){var n=e.type.contextTypes;if(!n)return kt;var r=e.stateNode;if(r&&r.__reactInternalMemoizedUnmaskedChildContext===t)return r.__reactInternalMemoizedMaskedChildContext;var s={},i;for(i in n)s[i]=t[i];return r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=t,e.__reactInternalMemoizedMaskedChildContext=s),s}function ve(e){return e=e.childContextTypes,e!=null}function os(){V(ye),V(ce)}function Bo(e,t,n){if(ce.current!==kt)throw Error(k(168));U(ce,t),U(ye,n)}function Iu(e,t,n){var r=e.stateNode;if(t=t.childContextTypes,typeof r.getChildContext!="function")return n;r=r.getChildContext();for(var s in r)if(!(s in t))throw Error(k(108,Sd(e)||"Unknown",s));return Q({},n,r)}function as(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||kt,Ft=ce.current,U(ce,e),U(ye,ye.current),!0}function Wo(e,t,n){var r=e.stateNode;if(!r)throw Error(k(169));n?(e=Iu(e,t,Ft),r.__reactInternalMemoizedMergedChildContext=e,V(ye),V(ce),U(ce,e)):V(ye),U(ye,n)}var Xe=null,Ds=!1,ll=!1;function zu(e){Xe===null?Xe=[e]:Xe.push(e)}function Ff(e){Ds=!0,zu(e)}function Et(){if(!ll&&Xe!==null){ll=!0;var e=0,t=$;try{var n=Xe;for($=1;e<n.length;e++){var r=n[e];do r=r(!0);while(r!==null)}Xe=null,Ds=!1}catch(s){throw Xe!==null&&(Xe=Xe.slice(e+1)),ou(bi,Et),s}finally{$=t,ll=!1}}return null}var sn=[],ln=0,us=null,cs=0,Te=[],Me=0,Vt=null,qe=1,Ze="";function _t(e,t){sn[ln++]=cs,sn[ln++]=us,us=e,cs=t}function Ru(e,t,n){Te[Me++]=qe,Te[Me++]=Ze,Te[Me++]=Vt,Vt=e;var r=qe;e=Ze;var s=32-Ue(r)-1;r&=~(1<<s),n+=1;var i=32-Ue(t)+s;if(30<i){var o=s-s%5;i=(r&(1<<o)-1).toString(32),r>>=o,s-=o,qe=1<<32-Ue(t)+s|n<<s|r,Ze=i+e}else qe=1<<i|n<<s|r,Ze=e}function Ii(e){e.return!==null&&(_t(e,1),Ru(e,1,0))}function zi(e){for(;e===us;)us=sn[--ln],sn[ln]=null,cs=sn[--ln],sn[ln]=null;for(;e===Vt;)Vt=Te[--Me],Te[Me]=null,Ze=Te[--Me],Te[Me]=null,qe=Te[--Me],Te[Me]=null}var ke=null,Se=null,H=!1,Oe=null;function $u(e,t){var n=De(5,null,null,0);n.elementType="DELETED",n.stateNode=t,n.return=e,t=e.deletions,t===null?(e.deletions=[n],e.flags|=16):t.push(n)}function Qo(e,t){switch(e.tag){case 5:var n=e.type;return t=t.nodeType!==1||n.toLowerCase()!==t.nodeName.toLowerCase()?null:t,t!==null?(e.stateNode=t,ke=e,Se=gt(t.firstChild),!0):!1;case 6:return t=e.pendingProps===""||t.nodeType!==3?null:t,t!==null?(e.stateNode=t,ke=e,Se=null,!0):!1;case 13:return t=t.nodeType!==8?null:t,t!==null?(n=Vt!==null?{id:qe,overflow:Ze}:null,e.memoizedState={dehydrated:t,treeContext:n,retryLane:1073741824},n=De(18,null,null,0),n.stateNode=t,n.return=e,e.child=n,ke=e,Se=null,!0):!1;default:return!1}}function Hl(e){return(e.mode&1)!==0&&(e.flags&128)===0}function Bl(e){if(H){var t=Se;if(t){var n=t;if(!Qo(e,t)){if(Hl(e))throw Error(k(418));t=gt(n.nextSibling);var r=ke;t&&Qo(e,t)?$u(r,n):(e.flags=e.flags&-4097|2,H=!1,ke=e)}}else{if(Hl(e))throw Error(k(418));e.flags=e.flags&-4097|2,H=!1,ke=e}}}function Ko(e){for(e=e.return;e!==null&&e.tag!==5&&e.tag!==3&&e.tag!==13;)e=e.return;ke=e}function _r(e){if(e!==ke)return!1;if(!H)return Ko(e),H=!0,!1;var t;if((t=e.tag!==3)&&!(t=e.tag!==5)&&(t=e.type,t=t!=="head"&&t!=="body"&&!Ol(e.type,e.memoizedProps)),t&&(t=Se)){if(Hl(e))throw Ou(),Error(k(418));for(;t;)$u(e,t),t=gt(t.nextSibling)}if(Ko(e),e.tag===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(k(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="/$"){if(t===0){Se=gt(e.nextSibling);break e}t--}else n!=="$"&&n!=="$!"&&n!=="$?"||t++}e=e.nextSibling}Se=null}}else Se=ke?gt(e.stateNode.nextSibling):null;return!0}function Ou(){for(var e=Se;e;)e=gt(e.nextSibling)}function yn(){Se=ke=null,H=!1}function Ri(e){Oe===null?Oe=[e]:Oe.push(e)}var Vf=lt.ReactCurrentBatchConfig;function Pn(e,t,n){if(e=n.ref,e!==null&&typeof e!="function"&&typeof e!="object"){if(n._owner){if(n=n._owner,n){if(n.tag!==1)throw Error(k(309));var r=n.stateNode}if(!r)throw Error(k(147,e));var s=r,i=""+e;return t!==null&&t.ref!==null&&typeof t.ref=="function"&&t.ref._stringRef===i?t.ref:(t=function(o){var a=s.refs;o===null?delete a[i]:a[i]=o},t._stringRef=i,t)}if(typeof e!="string")throw Error(k(284));if(!n._owner)throw Error(k(290,e))}return e}function Ar(e,t){throw e=Object.prototype.toString.call(t),Error(k(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function Go(e){var t=e._init;return t(e._payload)}function Uu(e){function t(c,d){if(e){var f=c.deletions;f===null?(c.deletions=[d],c.flags|=16):f.push(d)}}function n(c,d){if(!e)return null;for(;d!==null;)t(c,d),d=d.sibling;return null}function r(c,d){for(c=new Map;d!==null;)d.key!==null?c.set(d.key,d):c.set(d.index,d),d=d.sibling;return c}function s(c,d){return c=wt(c,d),c.index=0,c.sibling=null,c}function i(c,d,f){return c.index=f,e?(f=c.alternate,f!==null?(f=f.index,f<d?(c.flags|=2,d):f):(c.flags|=2,d)):(c.flags|=1048576,d)}function o(c){return e&&c.alternate===null&&(c.flags|=2),c}function a(c,d,f,h){return d===null||d.tag!==6?(d=fl(f,c.mode,h),d.return=c,d):(d=s(d,f),d.return=c,d)}function u(c,d,f,h){var N=f.type;return N===Zt?x(c,d,f.props.children,h,f.key):d!==null&&(d.elementType===N||typeof N=="object"&&N!==null&&N.$$typeof===ot&&Go(N)===d.type)?(h=s(d,f.props),h.ref=Pn(c,d,f),h.return=c,h):(h=Xr(f.type,f.key,f.props,null,c.mode,h),h.ref=Pn(c,d,f),h.return=c,h)}function m(c,d,f,h){return d===null||d.tag!==4||d.stateNode.containerInfo!==f.containerInfo||d.stateNode.implementation!==f.implementation?(d=ml(f,c.mode,h),d.return=c,d):(d=s(d,f.children||[]),d.return=c,d)}function x(c,d,f,h,N){return d===null||d.tag!==7?(d=Ut(f,c.mode,h,N),d.return=c,d):(d=s(d,f),d.return=c,d)}function y(c,d,f){if(typeof d=="string"&&d!==""||typeof d=="number")return d=fl(""+d,c.mode,f),d.return=c,d;if(typeof d=="object"&&d!==null){switch(d.$$typeof){case Nr:return f=Xr(d.type,d.key,d.props,null,c.mode,f),f.ref=Pn(c,null,d),f.return=c,f;case qt:return d=ml(d,c.mode,f),d.return=c,d;case ot:var h=d._init;return y(c,h(d._payload),f)}if($n(d)||Tn(d))return d=Ut(d,c.mode,f,null),d.return=c,d;Ar(c,d)}return null}function v(c,d,f,h){var N=d!==null?d.key:null;if(typeof f=="string"&&f!==""||typeof f=="number")return N!==null?null:a(c,d,""+f,h);if(typeof f=="object"&&f!==null){switch(f.$$typeof){case Nr:return f.key===N?u(c,d,f,h):null;case qt:return f.key===N?m(c,d,f,h):null;case ot:return N=f._init,v(c,d,N(f._payload),h)}if($n(f)||Tn(f))return N!==null?null:x(c,d,f,h,null);Ar(c,f)}return null}function w(c,d,f,h,N){if(typeof h=="string"&&h!==""||typeof h=="number")return c=c.get(f)||null,a(d,c,""+h,N);if(typeof h=="object"&&h!==null){switch(h.$$typeof){case Nr:return c=c.get(h.key===null?f:h.key)||null,u(d,c,h,N);case qt:return c=c.get(h.key===null?f:h.key)||null,m(d,c,h,N);case ot:var C=h._init;return w(c,d,f,C(h._payload),N)}if($n(h)||Tn(h))return c=c.get(f)||null,x(d,c,h,N,null);Ar(d,h)}return null}function j(c,d,f,h){for(var N=null,C=null,S=d,b=d=0,A=null;S!==null&&b<f.length;b++){S.index>b?(A=S,S=null):A=S.sibling;var M=v(c,S,f[b],h);if(M===null){S===null&&(S=A);break}e&&S&&M.alternate===null&&t(c,S),d=i(M,d,b),C===null?N=M:C.sibling=M,C=M,S=A}if(b===f.length)return n(c,S),H&&_t(c,b),N;if(S===null){for(;b<f.length;b++)S=y(c,f[b],h),S!==null&&(d=i(S,d,b),C===null?N=S:C.sibling=S,C=S);return H&&_t(c,b),N}for(S=r(c,S);b<f.length;b++)A=w(S,c,b,f[b],h),A!==null&&(e&&A.alternate!==null&&S.delete(A.key===null?b:A.key),d=i(A,d,b),C===null?N=A:C.sibling=A,C=A);return e&&S.forEach(function(je){return t(c,je)}),H&&_t(c,b),N}function p(c,d,f,h){var N=Tn(f);if(typeof N!="function")throw Error(k(150));if(f=N.call(f),f==null)throw Error(k(151));for(var C=N=null,S=d,b=d=0,A=null,M=f.next();S!==null&&!M.done;b++,M=f.next()){S.index>b?(A=S,S=null):A=S.sibling;var je=v(c,S,M.value,h);if(je===null){S===null&&(S=A);break}e&&S&&je.alternate===null&&t(c,S),d=i(je,d,b),C===null?N=je:C.sibling=je,C=je,S=A}if(M.done)return n(c,S),H&&_t(c,b),N;if(S===null){for(;!M.done;b++,M=f.next())M=y(c,M.value,h),M!==null&&(d=i(M,d,b),C===null?N=M:C.sibling=M,C=M);return H&&_t(c,b),N}for(S=r(c,S);!M.done;b++,M=f.next())M=w(S,c,b,M.value,h),M!==null&&(e&&M.alternate!==null&&S.delete(M.key===null?b:M.key),d=i(M,d,b),C===null?N=M:C.sibling=M,C=M);return e&&S.forEach(function(he){return t(c,he)}),H&&_t(c,b),N}function g(c,d,f,h){if(typeof f=="object"&&f!==null&&f.type===Zt&&f.key===null&&(f=f.props.children),typeof f=="object"&&f!==null){switch(f.$$typeof){case Nr:e:{for(var N=f.key,C=d;C!==null;){if(C.key===N){if(N=f.type,N===Zt){if(C.tag===7){n(c,C.sibling),d=s(C,f.props.children),d.return=c,c=d;break e}}else if(C.elementType===N||typeof N=="object"&&N!==null&&N.$$typeof===ot&&Go(N)===C.type){n(c,C.sibling),d=s(C,f.props),d.ref=Pn(c,C,f),d.return=c,c=d;break e}n(c,C);break}else t(c,C);C=C.sibling}f.type===Zt?(d=Ut(f.props.children,c.mode,h,f.key),d.return=c,c=d):(h=Xr(f.type,f.key,f.props,null,c.mode,h),h.ref=Pn(c,d,f),h.return=c,c=h)}return o(c);case qt:e:{for(C=f.key;d!==null;){if(d.key===C)if(d.tag===4&&d.stateNode.containerInfo===f.containerInfo&&d.stateNode.implementation===f.implementation){n(c,d.sibling),d=s(d,f.children||[]),d.return=c,c=d;break e}else{n(c,d);break}else t(c,d);d=d.sibling}d=ml(f,c.mode,h),d.return=c,c=d}return o(c);case ot:return C=f._init,g(c,d,C(f._payload),h)}if($n(f))return j(c,d,f,h);if(Tn(f))return p(c,d,f,h);Ar(c,f)}return typeof f=="string"&&f!==""||typeof f=="number"?(f=""+f,d!==null&&d.tag===6?(n(c,d.sibling),d=s(d,f),d.return=c,c=d):(n(c,d),d=fl(f,c.mode,h),d.return=c,c=d),o(c)):n(c,d)}return g}var vn=Uu(!0),Fu=Uu(!1),ds=bt(null),fs=null,on=null,$i=null;function Oi(){$i=on=fs=null}function Ui(e){var t=ds.current;V(ds),e._currentValue=t}function Wl(e,t,n){for(;e!==null;){var r=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,r!==null&&(r.childLanes|=t)):r!==null&&(r.childLanes&t)!==t&&(r.childLanes|=t),e===n)break;e=e.return}}function pn(e,t){fs=e,$i=on=null,e=e.dependencies,e!==null&&e.firstContext!==null&&(e.lanes&t&&(xe=!0),e.firstContext=null)}function Ae(e){var t=e._currentValue;if($i!==e)if(e={context:e,memoizedValue:t,next:null},on===null){if(fs===null)throw Error(k(308));on=e,fs.dependencies={lanes:0,firstContext:e}}else on=on.next=e;return t}var Rt=null;function Fi(e){Rt===null?Rt=[e]:Rt.push(e)}function Vu(e,t,n,r){var s=t.interleaved;return s===null?(n.next=n,Fi(t)):(n.next=s.next,s.next=n),t.interleaved=n,rt(e,r)}function rt(e,t){e.lanes|=t;var n=e.alternate;for(n!==null&&(n.lanes|=t),n=e,e=e.return;e!==null;)e.childLanes|=t,n=e.alternate,n!==null&&(n.childLanes|=t),n=e,e=e.return;return n.tag===3?n.stateNode:null}var at=!1;function Vi(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function Hu(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,effects:e.effects})}function Je(e,t){return{eventTime:e,lane:t,tag:0,payload:null,callback:null,next:null}}function xt(e,t,n){var r=e.updateQueue;if(r===null)return null;if(r=r.shared,z&2){var s=r.pending;return s===null?t.next=t:(t.next=s.next,s.next=t),r.pending=t,rt(e,n)}return s=r.interleaved,s===null?(t.next=t,Fi(r)):(t.next=s.next,s.next=t),r.interleaved=t,rt(e,n)}function Br(e,t,n){if(t=t.updateQueue,t!==null&&(t=t.shared,(n&4194240)!==0)){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,Ei(e,n)}}function Yo(e,t){var n=e.updateQueue,r=e.alternate;if(r!==null&&(r=r.updateQueue,n===r)){var s=null,i=null;if(n=n.firstBaseUpdate,n!==null){do{var o={eventTime:n.eventTime,lane:n.lane,tag:n.tag,payload:n.payload,callback:n.callback,next:null};i===null?s=i=o:i=i.next=o,n=n.next}while(n!==null);i===null?s=i=t:i=i.next=t}else s=i=t;n={baseState:r.baseState,firstBaseUpdate:s,lastBaseUpdate:i,shared:r.shared,effects:r.effects},e.updateQueue=n;return}e=n.lastBaseUpdate,e===null?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}function ms(e,t,n,r){var s=e.updateQueue;at=!1;var i=s.firstBaseUpdate,o=s.lastBaseUpdate,a=s.shared.pending;if(a!==null){s.shared.pending=null;var u=a,m=u.next;u.next=null,o===null?i=m:o.next=m,o=u;var x=e.alternate;x!==null&&(x=x.updateQueue,a=x.lastBaseUpdate,a!==o&&(a===null?x.firstBaseUpdate=m:a.next=m,x.lastBaseUpdate=u))}if(i!==null){var y=s.baseState;o=0,x=m=u=null,a=i;do{var v=a.lane,w=a.eventTime;if((r&v)===v){x!==null&&(x=x.next={eventTime:w,lane:0,tag:a.tag,payload:a.payload,callback:a.callback,next:null});e:{var j=e,p=a;switch(v=t,w=n,p.tag){case 1:if(j=p.payload,typeof j=="function"){y=j.call(w,y,v);break e}y=j;break e;case 3:j.flags=j.flags&-65537|128;case 0:if(j=p.payload,v=typeof j=="function"?j.call(w,y,v):j,v==null)break e;y=Q({},y,v);break e;case 2:at=!0}}a.callback!==null&&a.lane!==0&&(e.flags|=64,v=s.effects,v===null?s.effects=[a]:v.push(a))}else w={eventTime:w,lane:v,tag:a.tag,payload:a.payload,callback:a.callback,next:null},x===null?(m=x=w,u=y):x=x.next=w,o|=v;if(a=a.next,a===null){if(a=s.shared.pending,a===null)break;v=a,a=v.next,v.next=null,s.lastBaseUpdate=v,s.shared.pending=null}}while(1);if(x===null&&(u=y),s.baseState=u,s.firstBaseUpdate=m,s.lastBaseUpdate=x,t=s.shared.interleaved,t!==null){s=t;do o|=s.lane,s=s.next;while(s!==t)}else i===null&&(s.shared.lanes=0);Bt|=o,e.lanes=o,e.memoizedState=y}}function Xo(e,t,n){if(e=t.effects,t.effects=null,e!==null)for(t=0;t<e.length;t++){var r=e[t],s=r.callback;if(s!==null){if(r.callback=null,r=n,typeof s!="function")throw Error(k(191,s));s.call(r)}}}var yr={},Ke=bt(yr),or=bt(yr),ar=bt(yr);function $t(e){if(e===yr)throw Error(k(174));return e}function Hi(e,t){switch(U(ar,t),U(or,e),U(Ke,yr),e=t.nodeType,e){case 9:case 11:t=(t=t.documentElement)?t.namespaceURI:Cl(null,"");break;default:e=e===8?t.parentNode:t,t=e.namespaceURI||null,e=e.tagName,t=Cl(t,e)}V(Ke),U(Ke,t)}function wn(){V(Ke),V(or),V(ar)}function Bu(e){$t(ar.current);var t=$t(Ke.current),n=Cl(t,e.type);t!==n&&(U(or,e),U(Ke,n))}function Bi(e){or.current===e&&(V(Ke),V(or))}var B=bt(0);function ps(e){for(var t=e;t!==null;){if(t.tag===13){var n=t.memoizedState;if(n!==null&&(n=n.dehydrated,n===null||n.data==="$?"||n.data==="$!"))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if(t.flags&128)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var il=[];function Wi(){for(var e=0;e<il.length;e++)il[e]._workInProgressVersionPrimary=null;il.length=0}var Wr=lt.ReactCurrentDispatcher,ol=lt.ReactCurrentBatchConfig,Ht=0,W=null,Z=null,te=null,hs=!1,Qn=!1,ur=0,Hf=0;function oe(){throw Error(k(321))}function Qi(e,t){if(t===null)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!Ve(e[n],t[n]))return!1;return!0}function Ki(e,t,n,r,s,i){if(Ht=i,W=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,Wr.current=e===null||e.memoizedState===null?Kf:Gf,e=n(r,s),Qn){i=0;do{if(Qn=!1,ur=0,25<=i)throw Error(k(301));i+=1,te=Z=null,t.updateQueue=null,Wr.current=Yf,e=n(r,s)}while(Qn)}if(Wr.current=gs,t=Z!==null&&Z.next!==null,Ht=0,te=Z=W=null,hs=!1,t)throw Error(k(300));return e}function Gi(){var e=ur!==0;return ur=0,e}function Be(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return te===null?W.memoizedState=te=e:te=te.next=e,te}function Pe(){if(Z===null){var e=W.alternate;e=e!==null?e.memoizedState:null}else e=Z.next;var t=te===null?W.memoizedState:te.next;if(t!==null)te=t,Z=e;else{if(e===null)throw Error(k(310));Z=e,e={memoizedState:Z.memoizedState,baseState:Z.baseState,baseQueue:Z.baseQueue,queue:Z.queue,next:null},te===null?W.memoizedState=te=e:te=te.next=e}return te}function cr(e,t){return typeof t=="function"?t(e):t}function al(e){var t=Pe(),n=t.queue;if(n===null)throw Error(k(311));n.lastRenderedReducer=e;var r=Z,s=r.baseQueue,i=n.pending;if(i!==null){if(s!==null){var o=s.next;s.next=i.next,i.next=o}r.baseQueue=s=i,n.pending=null}if(s!==null){i=s.next,r=r.baseState;var a=o=null,u=null,m=i;do{var x=m.lane;if((Ht&x)===x)u!==null&&(u=u.next={lane:0,action:m.action,hasEagerState:m.hasEagerState,eagerState:m.eagerState,next:null}),r=m.hasEagerState?m.eagerState:e(r,m.action);else{var y={lane:x,action:m.action,hasEagerState:m.hasEagerState,eagerState:m.eagerState,next:null};u===null?(a=u=y,o=r):u=u.next=y,W.lanes|=x,Bt|=x}m=m.next}while(m!==null&&m!==i);u===null?o=r:u.next=a,Ve(r,t.memoizedState)||(xe=!0),t.memoizedState=r,t.baseState=o,t.baseQueue=u,n.lastRenderedState=r}if(e=n.interleaved,e!==null){s=e;do i=s.lane,W.lanes|=i,Bt|=i,s=s.next;while(s!==e)}else s===null&&(n.lanes=0);return[t.memoizedState,n.dispatch]}function ul(e){var t=Pe(),n=t.queue;if(n===null)throw Error(k(311));n.lastRenderedReducer=e;var r=n.dispatch,s=n.pending,i=t.memoizedState;if(s!==null){n.pending=null;var o=s=s.next;do i=e(i,o.action),o=o.next;while(o!==s);Ve(i,t.memoizedState)||(xe=!0),t.memoizedState=i,t.baseQueue===null&&(t.baseState=i),n.lastRenderedState=i}return[i,r]}function Wu(){}function Qu(e,t){var n=W,r=Pe(),s=t(),i=!Ve(r.memoizedState,s);if(i&&(r.memoizedState=s,xe=!0),r=r.queue,Yi(Yu.bind(null,n,r,e),[e]),r.getSnapshot!==t||i||te!==null&&te.memoizedState.tag&1){if(n.flags|=2048,dr(9,Gu.bind(null,n,r,s,t),void 0,null),ne===null)throw Error(k(349));Ht&30||Ku(n,t,s)}return s}function Ku(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},t=W.updateQueue,t===null?(t={lastEffect:null,stores:null},W.updateQueue=t,t.stores=[e]):(n=t.stores,n===null?t.stores=[e]:n.push(e))}function Gu(e,t,n,r){t.value=n,t.getSnapshot=r,Xu(t)&&qu(e)}function Yu(e,t,n){return n(function(){Xu(t)&&qu(e)})}function Xu(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!Ve(e,n)}catch{return!0}}function qu(e){var t=rt(e,1);t!==null&&Fe(t,e,1,-1)}function qo(e){var t=Be();return typeof e=="function"&&(e=e()),t.memoizedState=t.baseState=e,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:cr,lastRenderedState:e},t.queue=e,e=e.dispatch=Qf.bind(null,W,e),[t.memoizedState,e]}function dr(e,t,n,r){return e={tag:e,create:t,destroy:n,deps:r,next:null},t=W.updateQueue,t===null?(t={lastEffect:null,stores:null},W.updateQueue=t,t.lastEffect=e.next=e):(n=t.lastEffect,n===null?t.lastEffect=e.next=e:(r=n.next,n.next=e,e.next=r,t.lastEffect=e)),e}function Zu(){return Pe().memoizedState}function Qr(e,t,n,r){var s=Be();W.flags|=e,s.memoizedState=dr(1|t,n,void 0,r===void 0?null:r)}function _s(e,t,n,r){var s=Pe();r=r===void 0?null:r;var i=void 0;if(Z!==null){var o=Z.memoizedState;if(i=o.destroy,r!==null&&Qi(r,o.deps)){s.memoizedState=dr(t,n,i,r);return}}W.flags|=e,s.memoizedState=dr(1|t,n,i,r)}function Zo(e,t){return Qr(8390656,8,e,t)}function Yi(e,t){return _s(2048,8,e,t)}function Ju(e,t){return _s(4,2,e,t)}function ec(e,t){return _s(4,4,e,t)}function tc(e,t){if(typeof t=="function")return e=e(),t(e),function(){t(null)};if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function nc(e,t,n){return n=n!=null?n.concat([e]):null,_s(4,4,tc.bind(null,t,e),n)}function Xi(){}function rc(e,t){var n=Pe();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&Qi(t,r[1])?r[0]:(n.memoizedState=[e,t],e)}function sc(e,t){var n=Pe();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&Qi(t,r[1])?r[0]:(e=e(),n.memoizedState=[e,t],e)}function lc(e,t,n){return Ht&21?(Ve(n,t)||(n=cu(),W.lanes|=n,Bt|=n,e.baseState=!0),t):(e.baseState&&(e.baseState=!1,xe=!0),e.memoizedState=n)}function Bf(e,t){var n=$;$=n!==0&&4>n?n:4,e(!0);var r=ol.transition;ol.transition={};try{e(!1),t()}finally{$=n,ol.transition=r}}function ic(){return Pe().memoizedState}function Wf(e,t,n){var r=vt(e);if(n={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null},oc(e))ac(t,n);else if(n=Vu(e,t,n,r),n!==null){var s=fe();Fe(n,e,r,s),uc(n,t,r)}}function Qf(e,t,n){var r=vt(e),s={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null};if(oc(e))ac(t,s);else{var i=e.alternate;if(e.lanes===0&&(i===null||i.lanes===0)&&(i=t.lastRenderedReducer,i!==null))try{var o=t.lastRenderedState,a=i(o,n);if(s.hasEagerState=!0,s.eagerState=a,Ve(a,o)){var u=t.interleaved;u===null?(s.next=s,Fi(t)):(s.next=u.next,u.next=s),t.interleaved=s;return}}catch{}finally{}n=Vu(e,t,s,r),n!==null&&(s=fe(),Fe(n,e,r,s),uc(n,t,r))}}function oc(e){var t=e.alternate;return e===W||t!==null&&t===W}function ac(e,t){Qn=hs=!0;var n=e.pending;n===null?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function uc(e,t,n){if(n&4194240){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,Ei(e,n)}}var gs={readContext:Ae,useCallback:oe,useContext:oe,useEffect:oe,useImperativeHandle:oe,useInsertionEffect:oe,useLayoutEffect:oe,useMemo:oe,useReducer:oe,useRef:oe,useState:oe,useDebugValue:oe,useDeferredValue:oe,useTransition:oe,useMutableSource:oe,useSyncExternalStore:oe,useId:oe,unstable_isNewReconciler:!1},Kf={readContext:Ae,useCallback:function(e,t){return Be().memoizedState=[e,t===void 0?null:t],e},useContext:Ae,useEffect:Zo,useImperativeHandle:function(e,t,n){return n=n!=null?n.concat([e]):null,Qr(4194308,4,tc.bind(null,t,e),n)},useLayoutEffect:function(e,t){return Qr(4194308,4,e,t)},useInsertionEffect:function(e,t){return Qr(4,2,e,t)},useMemo:function(e,t){var n=Be();return t=t===void 0?null:t,e=e(),n.memoizedState=[e,t],e},useReducer:function(e,t,n){var r=Be();return t=n!==void 0?n(t):t,r.memoizedState=r.baseState=t,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:t},r.queue=e,e=e.dispatch=Wf.bind(null,W,e),[r.memoizedState,e]},useRef:function(e){var t=Be();return e={current:e},t.memoizedState=e},useState:qo,useDebugValue:Xi,useDeferredValue:function(e){return Be().memoizedState=e},useTransition:function(){var e=qo(!1),t=e[0];return e=Bf.bind(null,e[1]),Be().memoizedState=e,[t,e]},useMutableSource:function(){},useSyncExternalStore:function(e,t,n){var r=W,s=Be();if(H){if(n===void 0)throw Error(k(407));n=n()}else{if(n=t(),ne===null)throw Error(k(349));Ht&30||Ku(r,t,n)}s.memoizedState=n;var i={value:n,getSnapshot:t};return s.queue=i,Zo(Yu.bind(null,r,i,e),[e]),r.flags|=2048,dr(9,Gu.bind(null,r,i,n,t),void 0,null),n},useId:function(){var e=Be(),t=ne.identifierPrefix;if(H){var n=Ze,r=qe;n=(r&~(1<<32-Ue(r)-1)).toString(32)+n,t=":"+t+"R"+n,n=ur++,0<n&&(t+="H"+n.toString(32)),t+=":"}else n=Hf++,t=":"+t+"r"+n.toString(32)+":";return e.memoizedState=t},unstable_isNewReconciler:!1},Gf={readContext:Ae,useCallback:rc,useContext:Ae,useEffect:Yi,useImperativeHandle:nc,useInsertionEffect:Ju,useLayoutEffect:ec,useMemo:sc,useReducer:al,useRef:Zu,useState:function(){return al(cr)},useDebugValue:Xi,useDeferredValue:function(e){var t=Pe();return lc(t,Z.memoizedState,e)},useTransition:function(){var e=al(cr)[0],t=Pe().memoizedState;return[e,t]},useMutableSource:Wu,useSyncExternalStore:Qu,useId:ic,unstable_isNewReconciler:!1},Yf={readContext:Ae,useCallback:rc,useContext:Ae,useEffect:Yi,useImperativeHandle:nc,useInsertionEffect:Ju,useLayoutEffect:ec,useMemo:sc,useReducer:ul,useRef:Zu,useState:function(){return ul(cr)},useDebugValue:Xi,useDeferredValue:function(e){var t=Pe();return Z===null?t.memoizedState=e:lc(t,Z.memoizedState,e)},useTransition:function(){var e=ul(cr)[0],t=Pe().memoizedState;return[e,t]},useMutableSource:Wu,useSyncExternalStore:Qu,useId:ic,unstable_isNewReconciler:!1};function Re(e,t){if(e&&e.defaultProps){t=Q({},t),e=e.defaultProps;for(var n in e)t[n]===void 0&&(t[n]=e[n]);return t}return t}function Ql(e,t,n,r){t=e.memoizedState,n=n(r,t),n=n==null?t:Q({},t,n),e.memoizedState=n,e.lanes===0&&(e.updateQueue.baseState=n)}var As={isMounted:function(e){return(e=e._reactInternals)?Gt(e)===e:!1},enqueueSetState:function(e,t,n){e=e._reactInternals;var r=fe(),s=vt(e),i=Je(r,s);i.payload=t,n!=null&&(i.callback=n),t=xt(e,i,s),t!==null&&(Fe(t,e,s,r),Br(t,e,s))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var r=fe(),s=vt(e),i=Je(r,s);i.tag=1,i.payload=t,n!=null&&(i.callback=n),t=xt(e,i,s),t!==null&&(Fe(t,e,s,r),Br(t,e,s))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=fe(),r=vt(e),s=Je(n,r);s.tag=2,t!=null&&(s.callback=t),t=xt(e,s,r),t!==null&&(Fe(t,e,r,n),Br(t,e,r))}};function Jo(e,t,n,r,s,i,o){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(r,i,o):t.prototype&&t.prototype.isPureReactComponent?!rr(n,r)||!rr(s,i):!0}function cc(e,t,n){var r=!1,s=kt,i=t.contextType;return typeof i=="object"&&i!==null?i=Ae(i):(s=ve(t)?Ft:ce.current,r=t.contextTypes,i=(r=r!=null)?xn(e,s):kt),t=new t(n,i),e.memoizedState=t.state!==null&&t.state!==void 0?t.state:null,t.updater=As,e.stateNode=t,t._reactInternals=e,r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=s,e.__reactInternalMemoizedMaskedChildContext=i),t}function ea(e,t,n,r){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(n,r),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(n,r),t.state!==e&&As.enqueueReplaceState(t,t.state,null)}function Kl(e,t,n,r){var s=e.stateNode;s.props=n,s.state=e.memoizedState,s.refs={},Vi(e);var i=t.contextType;typeof i=="object"&&i!==null?s.context=Ae(i):(i=ve(t)?Ft:ce.current,s.context=xn(e,i)),s.state=e.memoizedState,i=t.getDerivedStateFromProps,typeof i=="function"&&(Ql(e,t,i,n),s.state=e.memoizedState),typeof t.getDerivedStateFromProps=="function"||typeof s.getSnapshotBeforeUpdate=="function"||typeof s.UNSAFE_componentWillMount!="function"&&typeof s.componentWillMount!="function"||(t=s.state,typeof s.componentWillMount=="function"&&s.componentWillMount(),typeof s.UNSAFE_componentWillMount=="function"&&s.UNSAFE_componentWillMount(),t!==s.state&&As.enqueueReplaceState(s,s.state,null),ms(e,n,s,r),s.state=e.memoizedState),typeof s.componentDidMount=="function"&&(e.flags|=4194308)}function jn(e,t){try{var n="",r=t;do n+=Nd(r),r=r.return;while(r);var s=n}catch(i){s=`
Error generating stack: `+i.message+`
`+i.stack}return{value:e,source:t,stack:s,digest:null}}function cl(e,t,n){return{value:e,source:null,stack:n??null,digest:t??null}}function Gl(e,t){try{console.error(t.value)}catch(n){setTimeout(function(){throw n})}}var Xf=typeof WeakMap=="function"?WeakMap:Map;function dc(e,t,n){n=Je(-1,n),n.tag=3,n.payload={element:null};var r=t.value;return n.callback=function(){ys||(ys=!0,si=r),Gl(e,t)},n}function fc(e,t,n){n=Je(-1,n),n.tag=3;var r=e.type.getDerivedStateFromError;if(typeof r=="function"){var s=t.value;n.payload=function(){return r(s)},n.callback=function(){Gl(e,t)}}var i=e.stateNode;return i!==null&&typeof i.componentDidCatch=="function"&&(n.callback=function(){Gl(e,t),typeof r!="function"&&(yt===null?yt=new Set([this]):yt.add(this));var o=t.stack;this.componentDidCatch(t.value,{componentStack:o!==null?o:""})}),n}function ta(e,t,n){var r=e.pingCache;if(r===null){r=e.pingCache=new Xf;var s=new Set;r.set(t,s)}else s=r.get(t),s===void 0&&(s=new Set,r.set(t,s));s.has(n)||(s.add(n),e=cm.bind(null,e,t,n),t.then(e,e))}function na(e){do{var t;if((t=e.tag===13)&&(t=e.memoizedState,t=t!==null?t.dehydrated!==null:!0),t)return e;e=e.return}while(e!==null);return null}function ra(e,t,n,r,s){return e.mode&1?(e.flags|=65536,e.lanes=s,e):(e===t?e.flags|=65536:(e.flags|=128,n.flags|=131072,n.flags&=-52805,n.tag===1&&(n.alternate===null?n.tag=17:(t=Je(-1,1),t.tag=2,xt(n,t,1))),n.lanes|=1),e)}var qf=lt.ReactCurrentOwner,xe=!1;function de(e,t,n,r){t.child=e===null?Fu(t,null,n,r):vn(t,e.child,n,r)}function sa(e,t,n,r,s){n=n.render;var i=t.ref;return pn(t,s),r=Ki(e,t,n,r,i,s),n=Gi(),e!==null&&!xe?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~s,st(e,t,s)):(H&&n&&Ii(t),t.flags|=1,de(e,t,r,s),t.child)}function la(e,t,n,r,s){if(e===null){var i=n.type;return typeof i=="function"&&!so(i)&&i.defaultProps===void 0&&n.compare===null&&n.defaultProps===void 0?(t.tag=15,t.type=i,mc(e,t,i,r,s)):(e=Xr(n.type,null,r,t,t.mode,s),e.ref=t.ref,e.return=t,t.child=e)}if(i=e.child,!(e.lanes&s)){var o=i.memoizedProps;if(n=n.compare,n=n!==null?n:rr,n(o,r)&&e.ref===t.ref)return st(e,t,s)}return t.flags|=1,e=wt(i,r),e.ref=t.ref,e.return=t,t.child=e}function mc(e,t,n,r,s){if(e!==null){var i=e.memoizedProps;if(rr(i,r)&&e.ref===t.ref)if(xe=!1,t.pendingProps=r=i,(e.lanes&s)!==0)e.flags&131072&&(xe=!0);else return t.lanes=e.lanes,st(e,t,s)}return Yl(e,t,n,r,s)}function pc(e,t,n){var r=t.pendingProps,s=r.children,i=e!==null?e.memoizedState:null;if(r.mode==="hidden")if(!(t.mode&1))t.memoizedState={baseLanes:0,cachePool:null,transitions:null},U(un,Ne),Ne|=n;else{if(!(n&1073741824))return e=i!==null?i.baseLanes|n:n,t.lanes=t.childLanes=1073741824,t.memoizedState={baseLanes:e,cachePool:null,transitions:null},t.updateQueue=null,U(un,Ne),Ne|=e,null;t.memoizedState={baseLanes:0,cachePool:null,transitions:null},r=i!==null?i.baseLanes:n,U(un,Ne),Ne|=r}else i!==null?(r=i.baseLanes|n,t.memoizedState=null):r=n,U(un,Ne),Ne|=r;return de(e,t,s,n),t.child}function hc(e,t){var n=t.ref;(e===null&&n!==null||e!==null&&e.ref!==n)&&(t.flags|=512,t.flags|=2097152)}function Yl(e,t,n,r,s){var i=ve(n)?Ft:ce.current;return i=xn(t,i),pn(t,s),n=Ki(e,t,n,r,i,s),r=Gi(),e!==null&&!xe?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~s,st(e,t,s)):(H&&r&&Ii(t),t.flags|=1,de(e,t,n,s),t.child)}function ia(e,t,n,r,s){if(ve(n)){var i=!0;as(t)}else i=!1;if(pn(t,s),t.stateNode===null)Kr(e,t),cc(t,n,r),Kl(t,n,r,s),r=!0;else if(e===null){var o=t.stateNode,a=t.memoizedProps;o.props=a;var u=o.context,m=n.contextType;typeof m=="object"&&m!==null?m=Ae(m):(m=ve(n)?Ft:ce.current,m=xn(t,m));var x=n.getDerivedStateFromProps,y=typeof x=="function"||typeof o.getSnapshotBeforeUpdate=="function";y||typeof o.UNSAFE_componentWillReceiveProps!="function"&&typeof o.componentWillReceiveProps!="function"||(a!==r||u!==m)&&ea(t,o,r,m),at=!1;var v=t.memoizedState;o.state=v,ms(t,r,o,s),u=t.memoizedState,a!==r||v!==u||ye.current||at?(typeof x=="function"&&(Ql(t,n,x,r),u=t.memoizedState),(a=at||Jo(t,n,a,r,v,u,m))?(y||typeof o.UNSAFE_componentWillMount!="function"&&typeof o.componentWillMount!="function"||(typeof o.componentWillMount=="function"&&o.componentWillMount(),typeof o.UNSAFE_componentWillMount=="function"&&o.UNSAFE_componentWillMount()),typeof o.componentDidMount=="function"&&(t.flags|=4194308)):(typeof o.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=r,t.memoizedState=u),o.props=r,o.state=u,o.context=m,r=a):(typeof o.componentDidMount=="function"&&(t.flags|=4194308),r=!1)}else{o=t.stateNode,Hu(e,t),a=t.memoizedProps,m=t.type===t.elementType?a:Re(t.type,a),o.props=m,y=t.pendingProps,v=o.context,u=n.contextType,typeof u=="object"&&u!==null?u=Ae(u):(u=ve(n)?Ft:ce.current,u=xn(t,u));var w=n.getDerivedStateFromProps;(x=typeof w=="function"||typeof o.getSnapshotBeforeUpdate=="function")||typeof o.UNSAFE_componentWillReceiveProps!="function"&&typeof o.componentWillReceiveProps!="function"||(a!==y||v!==u)&&ea(t,o,r,u),at=!1,v=t.memoizedState,o.state=v,ms(t,r,o,s);var j=t.memoizedState;a!==y||v!==j||ye.current||at?(typeof w=="function"&&(Ql(t,n,w,r),j=t.memoizedState),(m=at||Jo(t,n,m,r,v,j,u)||!1)?(x||typeof o.UNSAFE_componentWillUpdate!="function"&&typeof o.componentWillUpdate!="function"||(typeof o.componentWillUpdate=="function"&&o.componentWillUpdate(r,j,u),typeof o.UNSAFE_componentWillUpdate=="function"&&o.UNSAFE_componentWillUpdate(r,j,u)),typeof o.componentDidUpdate=="function"&&(t.flags|=4),typeof o.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof o.componentDidUpdate!="function"||a===e.memoizedProps&&v===e.memoizedState||(t.flags|=4),typeof o.getSnapshotBeforeUpdate!="function"||a===e.memoizedProps&&v===e.memoizedState||(t.flags|=1024),t.memoizedProps=r,t.memoizedState=j),o.props=r,o.state=j,o.context=u,r=m):(typeof o.componentDidUpdate!="function"||a===e.memoizedProps&&v===e.memoizedState||(t.flags|=4),typeof o.getSnapshotBeforeUpdate!="function"||a===e.memoizedProps&&v===e.memoizedState||(t.flags|=1024),r=!1)}return Xl(e,t,n,r,i,s)}function Xl(e,t,n,r,s,i){hc(e,t);var o=(t.flags&128)!==0;if(!r&&!o)return s&&Wo(t,n,!1),st(e,t,i);r=t.stateNode,qf.current=t;var a=o&&typeof n.getDerivedStateFromError!="function"?null:r.render();return t.flags|=1,e!==null&&o?(t.child=vn(t,e.child,null,i),t.child=vn(t,null,a,i)):de(e,t,a,i),t.memoizedState=r.state,s&&Wo(t,n,!0),t.child}function gc(e){var t=e.stateNode;t.pendingContext?Bo(e,t.pendingContext,t.pendingContext!==t.context):t.context&&Bo(e,t.context,!1),Hi(e,t.containerInfo)}function oa(e,t,n,r,s){return yn(),Ri(s),t.flags|=256,de(e,t,n,r),t.child}var ql={dehydrated:null,treeContext:null,retryLane:0};function Zl(e){return{baseLanes:e,cachePool:null,transitions:null}}function xc(e,t,n){var r=t.pendingProps,s=B.current,i=!1,o=(t.flags&128)!==0,a;if((a=o)||(a=e!==null&&e.memoizedState===null?!1:(s&2)!==0),a?(i=!0,t.flags&=-129):(e===null||e.memoizedState!==null)&&(s|=1),U(B,s&1),e===null)return Bl(t),e=t.memoizedState,e!==null&&(e=e.dehydrated,e!==null)?(t.mode&1?e.data==="$!"?t.lanes=8:t.lanes=1073741824:t.lanes=1,null):(o=r.children,e=r.fallback,i?(r=t.mode,i=t.child,o={mode:"hidden",children:o},!(r&1)&&i!==null?(i.childLanes=0,i.pendingProps=o):i=zs(o,r,0,null),e=Ut(e,r,n,null),i.return=t,e.return=t,i.sibling=e,t.child=i,t.child.memoizedState=Zl(n),t.memoizedState=ql,e):qi(t,o));if(s=e.memoizedState,s!==null&&(a=s.dehydrated,a!==null))return Zf(e,t,o,r,a,s,n);if(i){i=r.fallback,o=t.mode,s=e.child,a=s.sibling;var u={mode:"hidden",children:r.children};return!(o&1)&&t.child!==s?(r=t.child,r.childLanes=0,r.pendingProps=u,t.deletions=null):(r=wt(s,u),r.subtreeFlags=s.subtreeFlags&14680064),a!==null?i=wt(a,i):(i=Ut(i,o,n,null),i.flags|=2),i.return=t,r.return=t,r.sibling=i,t.child=r,r=i,i=t.child,o=e.child.memoizedState,o=o===null?Zl(n):{baseLanes:o.baseLanes|n,cachePool:null,transitions:o.transitions},i.memoizedState=o,i.childLanes=e.childLanes&~n,t.memoizedState=ql,r}return i=e.child,e=i.sibling,r=wt(i,{mode:"visible",children:r.children}),!(t.mode&1)&&(r.lanes=n),r.return=t,r.sibling=null,e!==null&&(n=t.deletions,n===null?(t.deletions=[e],t.flags|=16):n.push(e)),t.child=r,t.memoizedState=null,r}function qi(e,t){return t=zs({mode:"visible",children:t},e.mode,0,null),t.return=e,e.child=t}function Pr(e,t,n,r){return r!==null&&Ri(r),vn(t,e.child,null,n),e=qi(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function Zf(e,t,n,r,s,i,o){if(n)return t.flags&256?(t.flags&=-257,r=cl(Error(k(422))),Pr(e,t,o,r)):t.memoizedState!==null?(t.child=e.child,t.flags|=128,null):(i=r.fallback,s=t.mode,r=zs({mode:"visible",children:r.children},s,0,null),i=Ut(i,s,o,null),i.flags|=2,r.return=t,i.return=t,r.sibling=i,t.child=r,t.mode&1&&vn(t,e.child,null,o),t.child.memoizedState=Zl(o),t.memoizedState=ql,i);if(!(t.mode&1))return Pr(e,t,o,null);if(s.data==="$!"){if(r=s.nextSibling&&s.nextSibling.dataset,r)var a=r.dgst;return r=a,i=Error(k(419)),r=cl(i,r,void 0),Pr(e,t,o,r)}if(a=(o&e.childLanes)!==0,xe||a){if(r=ne,r!==null){switch(o&-o){case 4:s=2;break;case 16:s=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:s=32;break;case 536870912:s=268435456;break;default:s=0}s=s&(r.suspendedLanes|o)?0:s,s!==0&&s!==i.retryLane&&(i.retryLane=s,rt(e,s),Fe(r,e,s,-1))}return ro(),r=cl(Error(k(421))),Pr(e,t,o,r)}return s.data==="$?"?(t.flags|=128,t.child=e.child,t=dm.bind(null,e),s._reactRetry=t,null):(e=i.treeContext,Se=gt(s.nextSibling),ke=t,H=!0,Oe=null,e!==null&&(Te[Me++]=qe,Te[Me++]=Ze,Te[Me++]=Vt,qe=e.id,Ze=e.overflow,Vt=t),t=qi(t,r.children),t.flags|=4096,t)}function aa(e,t,n){e.lanes|=t;var r=e.alternate;r!==null&&(r.lanes|=t),Wl(e.return,t,n)}function dl(e,t,n,r,s){var i=e.memoizedState;i===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:r,tail:n,tailMode:s}:(i.isBackwards=t,i.rendering=null,i.renderingStartTime=0,i.last=r,i.tail=n,i.tailMode=s)}function yc(e,t,n){var r=t.pendingProps,s=r.revealOrder,i=r.tail;if(de(e,t,r.children,n),r=B.current,r&2)r=r&1|2,t.flags|=128;else{if(e!==null&&e.flags&128)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&aa(e,n,t);else if(e.tag===19)aa(e,n,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}r&=1}if(U(B,r),!(t.mode&1))t.memoizedState=null;else switch(s){case"forwards":for(n=t.child,s=null;n!==null;)e=n.alternate,e!==null&&ps(e)===null&&(s=n),n=n.sibling;n=s,n===null?(s=t.child,t.child=null):(s=n.sibling,n.sibling=null),dl(t,!1,s,n,i);break;case"backwards":for(n=null,s=t.child,t.child=null;s!==null;){if(e=s.alternate,e!==null&&ps(e)===null){t.child=s;break}e=s.sibling,s.sibling=n,n=s,s=e}dl(t,!0,n,null,i);break;case"together":dl(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function Kr(e,t){!(t.mode&1)&&e!==null&&(e.alternate=null,t.alternate=null,t.flags|=2)}function st(e,t,n){if(e!==null&&(t.dependencies=e.dependencies),Bt|=t.lanes,!(n&t.childLanes))return null;if(e!==null&&t.child!==e.child)throw Error(k(153));if(t.child!==null){for(e=t.child,n=wt(e,e.pendingProps),t.child=n,n.return=t;e.sibling!==null;)e=e.sibling,n=n.sibling=wt(e,e.pendingProps),n.return=t;n.sibling=null}return t.child}function Jf(e,t,n){switch(t.tag){case 3:gc(t),yn();break;case 5:Bu(t);break;case 1:ve(t.type)&&as(t);break;case 4:Hi(t,t.stateNode.containerInfo);break;case 10:var r=t.type._context,s=t.memoizedProps.value;U(ds,r._currentValue),r._currentValue=s;break;case 13:if(r=t.memoizedState,r!==null)return r.dehydrated!==null?(U(B,B.current&1),t.flags|=128,null):n&t.child.childLanes?xc(e,t,n):(U(B,B.current&1),e=st(e,t,n),e!==null?e.sibling:null);U(B,B.current&1);break;case 19:if(r=(n&t.childLanes)!==0,e.flags&128){if(r)return yc(e,t,n);t.flags|=128}if(s=t.memoizedState,s!==null&&(s.rendering=null,s.tail=null,s.lastEffect=null),U(B,B.current),r)break;return null;case 22:case 23:return t.lanes=0,pc(e,t,n)}return st(e,t,n)}var vc,Jl,wc,jc;vc=function(e,t){for(var n=t.child;n!==null;){if(n.tag===5||n.tag===6)e.appendChild(n.stateNode);else if(n.tag!==4&&n.child!==null){n.child.return=n,n=n.child;continue}if(n===t)break;for(;n.sibling===null;){if(n.return===null||n.return===t)return;n=n.return}n.sibling.return=n.return,n=n.sibling}};Jl=function(){};wc=function(e,t,n,r){var s=e.memoizedProps;if(s!==r){e=t.stateNode,$t(Ke.current);var i=null;switch(n){case"input":s=jl(e,s),r=jl(e,r),i=[];break;case"select":s=Q({},s,{value:void 0}),r=Q({},r,{value:void 0}),i=[];break;case"textarea":s=kl(e,s),r=kl(e,r),i=[];break;default:typeof s.onClick!="function"&&typeof r.onClick=="function"&&(e.onclick=is)}bl(n,r);var o;n=null;for(m in s)if(!r.hasOwnProperty(m)&&s.hasOwnProperty(m)&&s[m]!=null)if(m==="style"){var a=s[m];for(o in a)a.hasOwnProperty(o)&&(n||(n={}),n[o]="")}else m!=="dangerouslySetInnerHTML"&&m!=="children"&&m!=="suppressContentEditableWarning"&&m!=="suppressHydrationWarning"&&m!=="autoFocus"&&(Xn.hasOwnProperty(m)?i||(i=[]):(i=i||[]).push(m,null));for(m in r){var u=r[m];if(a=s!=null?s[m]:void 0,r.hasOwnProperty(m)&&u!==a&&(u!=null||a!=null))if(m==="style")if(a){for(o in a)!a.hasOwnProperty(o)||u&&u.hasOwnProperty(o)||(n||(n={}),n[o]="");for(o in u)u.hasOwnProperty(o)&&a[o]!==u[o]&&(n||(n={}),n[o]=u[o])}else n||(i||(i=[]),i.push(m,n)),n=u;else m==="dangerouslySetInnerHTML"?(u=u?u.__html:void 0,a=a?a.__html:void 0,u!=null&&a!==u&&(i=i||[]).push(m,u)):m==="children"?typeof u!="string"&&typeof u!="number"||(i=i||[]).push(m,""+u):m!=="suppressContentEditableWarning"&&m!=="suppressHydrationWarning"&&(Xn.hasOwnProperty(m)?(u!=null&&m==="onScroll"&&F("scroll",e),i||a===u||(i=[])):(i=i||[]).push(m,u))}n&&(i=i||[]).push("style",n);var m=i;(t.updateQueue=m)&&(t.flags|=4)}};jc=function(e,t,n,r){n!==r&&(t.flags|=4)};function In(e,t){if(!H)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;t!==null;)t.alternate!==null&&(n=t),t=t.sibling;n===null?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var r=null;n!==null;)n.alternate!==null&&(r=n),n=n.sibling;r===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:r.sibling=null}}function ae(e){var t=e.alternate!==null&&e.alternate.child===e.child,n=0,r=0;if(t)for(var s=e.child;s!==null;)n|=s.lanes|s.childLanes,r|=s.subtreeFlags&14680064,r|=s.flags&14680064,s.return=e,s=s.sibling;else for(s=e.child;s!==null;)n|=s.lanes|s.childLanes,r|=s.subtreeFlags,r|=s.flags,s.return=e,s=s.sibling;return e.subtreeFlags|=r,e.childLanes=n,t}function em(e,t,n){var r=t.pendingProps;switch(zi(t),t.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return ae(t),null;case 1:return ve(t.type)&&os(),ae(t),null;case 3:return r=t.stateNode,wn(),V(ye),V(ce),Wi(),r.pendingContext&&(r.context=r.pendingContext,r.pendingContext=null),(e===null||e.child===null)&&(_r(t)?t.flags|=4:e===null||e.memoizedState.isDehydrated&&!(t.flags&256)||(t.flags|=1024,Oe!==null&&(oi(Oe),Oe=null))),Jl(e,t),ae(t),null;case 5:Bi(t);var s=$t(ar.current);if(n=t.type,e!==null&&t.stateNode!=null)wc(e,t,n,r,s),e.ref!==t.ref&&(t.flags|=512,t.flags|=2097152);else{if(!r){if(t.stateNode===null)throw Error(k(166));return ae(t),null}if(e=$t(Ke.current),_r(t)){r=t.stateNode,n=t.type;var i=t.memoizedProps;switch(r[We]=t,r[ir]=i,e=(t.mode&1)!==0,n){case"dialog":F("cancel",r),F("close",r);break;case"iframe":case"object":case"embed":F("load",r);break;case"video":case"audio":for(s=0;s<Un.length;s++)F(Un[s],r);break;case"source":F("error",r);break;case"img":case"image":case"link":F("error",r),F("load",r);break;case"details":F("toggle",r);break;case"input":xo(r,i),F("invalid",r);break;case"select":r._wrapperState={wasMultiple:!!i.multiple},F("invalid",r);break;case"textarea":vo(r,i),F("invalid",r)}bl(n,i),s=null;for(var o in i)if(i.hasOwnProperty(o)){var a=i[o];o==="children"?typeof a=="string"?r.textContent!==a&&(i.suppressHydrationWarning!==!0&&Dr(r.textContent,a,e),s=["children",a]):typeof a=="number"&&r.textContent!==""+a&&(i.suppressHydrationWarning!==!0&&Dr(r.textContent,a,e),s=["children",""+a]):Xn.hasOwnProperty(o)&&a!=null&&o==="onScroll"&&F("scroll",r)}switch(n){case"input":Sr(r),yo(r,i,!0);break;case"textarea":Sr(r),wo(r);break;case"select":case"option":break;default:typeof i.onClick=="function"&&(r.onclick=is)}r=s,t.updateQueue=r,r!==null&&(t.flags|=4)}else{o=s.nodeType===9?s:s.ownerDocument,e==="http://www.w3.org/1999/xhtml"&&(e=Ya(n)),e==="http://www.w3.org/1999/xhtml"?n==="script"?(e=o.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):typeof r.is=="string"?e=o.createElement(n,{is:r.is}):(e=o.createElement(n),n==="select"&&(o=e,r.multiple?o.multiple=!0:r.size&&(o.size=r.size))):e=o.createElementNS(e,n),e[We]=t,e[ir]=r,vc(e,t,!1,!1),t.stateNode=e;e:{switch(o=El(n,r),n){case"dialog":F("cancel",e),F("close",e),s=r;break;case"iframe":case"object":case"embed":F("load",e),s=r;break;case"video":case"audio":for(s=0;s<Un.length;s++)F(Un[s],e);s=r;break;case"source":F("error",e),s=r;break;case"img":case"image":case"link":F("error",e),F("load",e),s=r;break;case"details":F("toggle",e),s=r;break;case"input":xo(e,r),s=jl(e,r),F("invalid",e);break;case"option":s=r;break;case"select":e._wrapperState={wasMultiple:!!r.multiple},s=Q({},r,{value:void 0}),F("invalid",e);break;case"textarea":vo(e,r),s=kl(e,r),F("invalid",e);break;default:s=r}bl(n,s),a=s;for(i in a)if(a.hasOwnProperty(i)){var u=a[i];i==="style"?Za(e,u):i==="dangerouslySetInnerHTML"?(u=u?u.__html:void 0,u!=null&&Xa(e,u)):i==="children"?typeof u=="string"?(n!=="textarea"||u!=="")&&qn(e,u):typeof u=="number"&&qn(e,""+u):i!=="suppressContentEditableWarning"&&i!=="suppressHydrationWarning"&&i!=="autoFocus"&&(Xn.hasOwnProperty(i)?u!=null&&i==="onScroll"&&F("scroll",e):u!=null&&ji(e,i,u,o))}switch(n){case"input":Sr(e),yo(e,r,!1);break;case"textarea":Sr(e),wo(e);break;case"option":r.value!=null&&e.setAttribute("value",""+St(r.value));break;case"select":e.multiple=!!r.multiple,i=r.value,i!=null?cn(e,!!r.multiple,i,!1):r.defaultValue!=null&&cn(e,!!r.multiple,r.defaultValue,!0);break;default:typeof s.onClick=="function"&&(e.onclick=is)}switch(n){case"button":case"input":case"select":case"textarea":r=!!r.autoFocus;break e;case"img":r=!0;break e;default:r=!1}}r&&(t.flags|=4)}t.ref!==null&&(t.flags|=512,t.flags|=2097152)}return ae(t),null;case 6:if(e&&t.stateNode!=null)jc(e,t,e.memoizedProps,r);else{if(typeof r!="string"&&t.stateNode===null)throw Error(k(166));if(n=$t(ar.current),$t(Ke.current),_r(t)){if(r=t.stateNode,n=t.memoizedProps,r[We]=t,(i=r.nodeValue!==n)&&(e=ke,e!==null))switch(e.tag){case 3:Dr(r.nodeValue,n,(e.mode&1)!==0);break;case 5:e.memoizedProps.suppressHydrationWarning!==!0&&Dr(r.nodeValue,n,(e.mode&1)!==0)}i&&(t.flags|=4)}else r=(n.nodeType===9?n:n.ownerDocument).createTextNode(r),r[We]=t,t.stateNode=r}return ae(t),null;case 13:if(V(B),r=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(H&&Se!==null&&t.mode&1&&!(t.flags&128))Ou(),yn(),t.flags|=98560,i=!1;else if(i=_r(t),r!==null&&r.dehydrated!==null){if(e===null){if(!i)throw Error(k(318));if(i=t.memoizedState,i=i!==null?i.dehydrated:null,!i)throw Error(k(317));i[We]=t}else yn(),!(t.flags&128)&&(t.memoizedState=null),t.flags|=4;ae(t),i=!1}else Oe!==null&&(oi(Oe),Oe=null),i=!0;if(!i)return t.flags&65536?t:null}return t.flags&128?(t.lanes=n,t):(r=r!==null,r!==(e!==null&&e.memoizedState!==null)&&r&&(t.child.flags|=8192,t.mode&1&&(e===null||B.current&1?J===0&&(J=3):ro())),t.updateQueue!==null&&(t.flags|=4),ae(t),null);case 4:return wn(),Jl(e,t),e===null&&sr(t.stateNode.containerInfo),ae(t),null;case 10:return Ui(t.type._context),ae(t),null;case 17:return ve(t.type)&&os(),ae(t),null;case 19:if(V(B),i=t.memoizedState,i===null)return ae(t),null;if(r=(t.flags&128)!==0,o=i.rendering,o===null)if(r)In(i,!1);else{if(J!==0||e!==null&&e.flags&128)for(e=t.child;e!==null;){if(o=ps(e),o!==null){for(t.flags|=128,In(i,!1),r=o.updateQueue,r!==null&&(t.updateQueue=r,t.flags|=4),t.subtreeFlags=0,r=n,n=t.child;n!==null;)i=n,e=r,i.flags&=14680066,o=i.alternate,o===null?(i.childLanes=0,i.lanes=e,i.child=null,i.subtreeFlags=0,i.memoizedProps=null,i.memoizedState=null,i.updateQueue=null,i.dependencies=null,i.stateNode=null):(i.childLanes=o.childLanes,i.lanes=o.lanes,i.child=o.child,i.subtreeFlags=0,i.deletions=null,i.memoizedProps=o.memoizedProps,i.memoizedState=o.memoizedState,i.updateQueue=o.updateQueue,i.type=o.type,e=o.dependencies,i.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),n=n.sibling;return U(B,B.current&1|2),t.child}e=e.sibling}i.tail!==null&&Y()>Nn&&(t.flags|=128,r=!0,In(i,!1),t.lanes=4194304)}else{if(!r)if(e=ps(o),e!==null){if(t.flags|=128,r=!0,n=e.updateQueue,n!==null&&(t.updateQueue=n,t.flags|=4),In(i,!0),i.tail===null&&i.tailMode==="hidden"&&!o.alternate&&!H)return ae(t),null}else 2*Y()-i.renderingStartTime>Nn&&n!==1073741824&&(t.flags|=128,r=!0,In(i,!1),t.lanes=4194304);i.isBackwards?(o.sibling=t.child,t.child=o):(n=i.last,n!==null?n.sibling=o:t.child=o,i.last=o)}return i.tail!==null?(t=i.tail,i.rendering=t,i.tail=t.sibling,i.renderingStartTime=Y(),t.sibling=null,n=B.current,U(B,r?n&1|2:n&1),t):(ae(t),null);case 22:case 23:return no(),r=t.memoizedState!==null,e!==null&&e.memoizedState!==null!==r&&(t.flags|=8192),r&&t.mode&1?Ne&1073741824&&(ae(t),t.subtreeFlags&6&&(t.flags|=8192)):ae(t),null;case 24:return null;case 25:return null}throw Error(k(156,t.tag))}function tm(e,t){switch(zi(t),t.tag){case 1:return ve(t.type)&&os(),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return wn(),V(ye),V(ce),Wi(),e=t.flags,e&65536&&!(e&128)?(t.flags=e&-65537|128,t):null;case 5:return Bi(t),null;case 13:if(V(B),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(k(340));yn()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return V(B),null;case 4:return wn(),null;case 10:return Ui(t.type._context),null;case 22:case 23:return no(),null;case 24:return null;default:return null}}var Ir=!1,ue=!1,nm=typeof WeakSet=="function"?WeakSet:Set,L=null;function an(e,t){var n=e.ref;if(n!==null)if(typeof n=="function")try{n(null)}catch(r){K(e,t,r)}else n.current=null}function ei(e,t,n){try{n()}catch(r){K(e,t,r)}}var ua=!1;function rm(e,t){if(Rl=rs,e=bu(),Pi(e)){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{n=(n=e.ownerDocument)&&n.defaultView||window;var r=n.getSelection&&n.getSelection();if(r&&r.rangeCount!==0){n=r.anchorNode;var s=r.anchorOffset,i=r.focusNode;r=r.focusOffset;try{n.nodeType,i.nodeType}catch{n=null;break e}var o=0,a=-1,u=-1,m=0,x=0,y=e,v=null;t:for(;;){for(var w;y!==n||s!==0&&y.nodeType!==3||(a=o+s),y!==i||r!==0&&y.nodeType!==3||(u=o+r),y.nodeType===3&&(o+=y.nodeValue.length),(w=y.firstChild)!==null;)v=y,y=w;for(;;){if(y===e)break t;if(v===n&&++m===s&&(a=o),v===i&&++x===r&&(u=o),(w=y.nextSibling)!==null)break;y=v,v=y.parentNode}y=w}n=a===-1||u===-1?null:{start:a,end:u}}else n=null}n=n||{start:0,end:0}}else n=null;for($l={focusedElem:e,selectionRange:n},rs=!1,L=t;L!==null;)if(t=L,e=t.child,(t.subtreeFlags&1028)!==0&&e!==null)e.return=t,L=e;else for(;L!==null;){t=L;try{var j=t.alternate;if(t.flags&1024)switch(t.tag){case 0:case 11:case 15:break;case 1:if(j!==null){var p=j.memoizedProps,g=j.memoizedState,c=t.stateNode,d=c.getSnapshotBeforeUpdate(t.elementType===t.type?p:Re(t.type,p),g);c.__reactInternalSnapshotBeforeUpdate=d}break;case 3:var f=t.stateNode.containerInfo;f.nodeType===1?f.textContent="":f.nodeType===9&&f.documentElement&&f.removeChild(f.documentElement);break;case 5:case 6:case 4:case 17:break;default:throw Error(k(163))}}catch(h){K(t,t.return,h)}if(e=t.sibling,e!==null){e.return=t.return,L=e;break}L=t.return}return j=ua,ua=!1,j}function Kn(e,t,n){var r=t.updateQueue;if(r=r!==null?r.lastEffect:null,r!==null){var s=r=r.next;do{if((s.tag&e)===e){var i=s.destroy;s.destroy=void 0,i!==void 0&&ei(t,n,i)}s=s.next}while(s!==r)}}function Ps(e,t){if(t=t.updateQueue,t=t!==null?t.lastEffect:null,t!==null){var n=t=t.next;do{if((n.tag&e)===e){var r=n.create;n.destroy=r()}n=n.next}while(n!==t)}}function ti(e){var t=e.ref;if(t!==null){var n=e.stateNode;switch(e.tag){case 5:e=n;break;default:e=n}typeof t=="function"?t(e):t.current=e}}function Nc(e){var t=e.alternate;t!==null&&(e.alternate=null,Nc(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&(delete t[We],delete t[ir],delete t[Fl],delete t[Of],delete t[Uf])),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}function Sc(e){return e.tag===5||e.tag===3||e.tag===4}function ca(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||Sc(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function ni(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.nodeType===8?n.parentNode.insertBefore(e,t):n.insertBefore(e,t):(n.nodeType===8?(t=n.parentNode,t.insertBefore(e,n)):(t=n,t.appendChild(e)),n=n._reactRootContainer,n!=null||t.onclick!==null||(t.onclick=is));else if(r!==4&&(e=e.child,e!==null))for(ni(e,t,n),e=e.sibling;e!==null;)ni(e,t,n),e=e.sibling}function ri(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(r!==4&&(e=e.child,e!==null))for(ri(e,t,n),e=e.sibling;e!==null;)ri(e,t,n),e=e.sibling}var se=null,$e=!1;function it(e,t,n){for(n=n.child;n!==null;)kc(e,t,n),n=n.sibling}function kc(e,t,n){if(Qe&&typeof Qe.onCommitFiberUnmount=="function")try{Qe.onCommitFiberUnmount(bs,n)}catch{}switch(n.tag){case 5:ue||an(n,t);case 6:var r=se,s=$e;se=null,it(e,t,n),se=r,$e=s,se!==null&&($e?(e=se,n=n.stateNode,e.nodeType===8?e.parentNode.removeChild(n):e.removeChild(n)):se.removeChild(n.stateNode));break;case 18:se!==null&&($e?(e=se,n=n.stateNode,e.nodeType===8?sl(e.parentNode,n):e.nodeType===1&&sl(e,n),tr(e)):sl(se,n.stateNode));break;case 4:r=se,s=$e,se=n.stateNode.containerInfo,$e=!0,it(e,t,n),se=r,$e=s;break;case 0:case 11:case 14:case 15:if(!ue&&(r=n.updateQueue,r!==null&&(r=r.lastEffect,r!==null))){s=r=r.next;do{var i=s,o=i.destroy;i=i.tag,o!==void 0&&(i&2||i&4)&&ei(n,t,o),s=s.next}while(s!==r)}it(e,t,n);break;case 1:if(!ue&&(an(n,t),r=n.stateNode,typeof r.componentWillUnmount=="function"))try{r.props=n.memoizedProps,r.state=n.memoizedState,r.componentWillUnmount()}catch(a){K(n,t,a)}it(e,t,n);break;case 21:it(e,t,n);break;case 22:n.mode&1?(ue=(r=ue)||n.memoizedState!==null,it(e,t,n),ue=r):it(e,t,n);break;default:it(e,t,n)}}function da(e){var t=e.updateQueue;if(t!==null){e.updateQueue=null;var n=e.stateNode;n===null&&(n=e.stateNode=new nm),t.forEach(function(r){var s=fm.bind(null,e,r);n.has(r)||(n.add(r),r.then(s,s))})}}function ze(e,t){var n=t.deletions;if(n!==null)for(var r=0;r<n.length;r++){var s=n[r];try{var i=e,o=t,a=o;e:for(;a!==null;){switch(a.tag){case 5:se=a.stateNode,$e=!1;break e;case 3:se=a.stateNode.containerInfo,$e=!0;break e;case 4:se=a.stateNode.containerInfo,$e=!0;break e}a=a.return}if(se===null)throw Error(k(160));kc(i,o,s),se=null,$e=!1;var u=s.alternate;u!==null&&(u.return=null),s.return=null}catch(m){K(s,t,m)}}if(t.subtreeFlags&12854)for(t=t.child;t!==null;)Cc(t,e),t=t.sibling}function Cc(e,t){var n=e.alternate,r=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:if(ze(t,e),He(e),r&4){try{Kn(3,e,e.return),Ps(3,e)}catch(p){K(e,e.return,p)}try{Kn(5,e,e.return)}catch(p){K(e,e.return,p)}}break;case 1:ze(t,e),He(e),r&512&&n!==null&&an(n,n.return);break;case 5:if(ze(t,e),He(e),r&512&&n!==null&&an(n,n.return),e.flags&32){var s=e.stateNode;try{qn(s,"")}catch(p){K(e,e.return,p)}}if(r&4&&(s=e.stateNode,s!=null)){var i=e.memoizedProps,o=n!==null?n.memoizedProps:i,a=e.type,u=e.updateQueue;if(e.updateQueue=null,u!==null)try{a==="input"&&i.type==="radio"&&i.name!=null&&Ka(s,i),El(a,o);var m=El(a,i);for(o=0;o<u.length;o+=2){var x=u[o],y=u[o+1];x==="style"?Za(s,y):x==="dangerouslySetInnerHTML"?Xa(s,y):x==="children"?qn(s,y):ji(s,x,y,m)}switch(a){case"input":Nl(s,i);break;case"textarea":Ga(s,i);break;case"select":var v=s._wrapperState.wasMultiple;s._wrapperState.wasMultiple=!!i.multiple;var w=i.value;w!=null?cn(s,!!i.multiple,w,!1):v!==!!i.multiple&&(i.defaultValue!=null?cn(s,!!i.multiple,i.defaultValue,!0):cn(s,!!i.multiple,i.multiple?[]:"",!1))}s[ir]=i}catch(p){K(e,e.return,p)}}break;case 6:if(ze(t,e),He(e),r&4){if(e.stateNode===null)throw Error(k(162));s=e.stateNode,i=e.memoizedProps;try{s.nodeValue=i}catch(p){K(e,e.return,p)}}break;case 3:if(ze(t,e),He(e),r&4&&n!==null&&n.memoizedState.isDehydrated)try{tr(t.containerInfo)}catch(p){K(e,e.return,p)}break;case 4:ze(t,e),He(e);break;case 13:ze(t,e),He(e),s=e.child,s.flags&8192&&(i=s.memoizedState!==null,s.stateNode.isHidden=i,!i||s.alternate!==null&&s.alternate.memoizedState!==null||(eo=Y())),r&4&&da(e);break;case 22:if(x=n!==null&&n.memoizedState!==null,e.mode&1?(ue=(m=ue)||x,ze(t,e),ue=m):ze(t,e),He(e),r&8192){if(m=e.memoizedState!==null,(e.stateNode.isHidden=m)&&!x&&e.mode&1)for(L=e,x=e.child;x!==null;){for(y=L=x;L!==null;){switch(v=L,w=v.child,v.tag){case 0:case 11:case 14:case 15:Kn(4,v,v.return);break;case 1:an(v,v.return);var j=v.stateNode;if(typeof j.componentWillUnmount=="function"){r=v,n=v.return;try{t=r,j.props=t.memoizedProps,j.state=t.memoizedState,j.componentWillUnmount()}catch(p){K(r,n,p)}}break;case 5:an(v,v.return);break;case 22:if(v.memoizedState!==null){ma(y);continue}}w!==null?(w.return=v,L=w):ma(y)}x=x.sibling}e:for(x=null,y=e;;){if(y.tag===5){if(x===null){x=y;try{s=y.stateNode,m?(i=s.style,typeof i.setProperty=="function"?i.setProperty("display","none","important"):i.display="none"):(a=y.stateNode,u=y.memoizedProps.style,o=u!=null&&u.hasOwnProperty("display")?u.display:null,a.style.display=qa("display",o))}catch(p){K(e,e.return,p)}}}else if(y.tag===6){if(x===null)try{y.stateNode.nodeValue=m?"":y.memoizedProps}catch(p){K(e,e.return,p)}}else if((y.tag!==22&&y.tag!==23||y.memoizedState===null||y===e)&&y.child!==null){y.child.return=y,y=y.child;continue}if(y===e)break e;for(;y.sibling===null;){if(y.return===null||y.return===e)break e;x===y&&(x=null),y=y.return}x===y&&(x=null),y.sibling.return=y.return,y=y.sibling}}break;case 19:ze(t,e),He(e),r&4&&da(e);break;case 21:break;default:ze(t,e),He(e)}}function He(e){var t=e.flags;if(t&2){try{e:{for(var n=e.return;n!==null;){if(Sc(n)){var r=n;break e}n=n.return}throw Error(k(160))}switch(r.tag){case 5:var s=r.stateNode;r.flags&32&&(qn(s,""),r.flags&=-33);var i=ca(e);ri(e,i,s);break;case 3:case 4:var o=r.stateNode.containerInfo,a=ca(e);ni(e,a,o);break;default:throw Error(k(161))}}catch(u){K(e,e.return,u)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function sm(e,t,n){L=e,bc(e)}function bc(e,t,n){for(var r=(e.mode&1)!==0;L!==null;){var s=L,i=s.child;if(s.tag===22&&r){var o=s.memoizedState!==null||Ir;if(!o){var a=s.alternate,u=a!==null&&a.memoizedState!==null||ue;a=Ir;var m=ue;if(Ir=o,(ue=u)&&!m)for(L=s;L!==null;)o=L,u=o.child,o.tag===22&&o.memoizedState!==null?pa(s):u!==null?(u.return=o,L=u):pa(s);for(;i!==null;)L=i,bc(i),i=i.sibling;L=s,Ir=a,ue=m}fa(e)}else s.subtreeFlags&8772&&i!==null?(i.return=s,L=i):fa(e)}}function fa(e){for(;L!==null;){var t=L;if(t.flags&8772){var n=t.alternate;try{if(t.flags&8772)switch(t.tag){case 0:case 11:case 15:ue||Ps(5,t);break;case 1:var r=t.stateNode;if(t.flags&4&&!ue)if(n===null)r.componentDidMount();else{var s=t.elementType===t.type?n.memoizedProps:Re(t.type,n.memoizedProps);r.componentDidUpdate(s,n.memoizedState,r.__reactInternalSnapshotBeforeUpdate)}var i=t.updateQueue;i!==null&&Xo(t,i,r);break;case 3:var o=t.updateQueue;if(o!==null){if(n=null,t.child!==null)switch(t.child.tag){case 5:n=t.child.stateNode;break;case 1:n=t.child.stateNode}Xo(t,o,n)}break;case 5:var a=t.stateNode;if(n===null&&t.flags&4){n=a;var u=t.memoizedProps;switch(t.type){case"button":case"input":case"select":case"textarea":u.autoFocus&&n.focus();break;case"img":u.src&&(n.src=u.src)}}break;case 6:break;case 4:break;case 12:break;case 13:if(t.memoizedState===null){var m=t.alternate;if(m!==null){var x=m.memoizedState;if(x!==null){var y=x.dehydrated;y!==null&&tr(y)}}}break;case 19:case 17:case 21:case 22:case 23:case 25:break;default:throw Error(k(163))}ue||t.flags&512&&ti(t)}catch(v){K(t,t.return,v)}}if(t===e){L=null;break}if(n=t.sibling,n!==null){n.return=t.return,L=n;break}L=t.return}}function ma(e){for(;L!==null;){var t=L;if(t===e){L=null;break}var n=t.sibling;if(n!==null){n.return=t.return,L=n;break}L=t.return}}function pa(e){for(;L!==null;){var t=L;try{switch(t.tag){case 0:case 11:case 15:var n=t.return;try{Ps(4,t)}catch(u){K(t,n,u)}break;case 1:var r=t.stateNode;if(typeof r.componentDidMount=="function"){var s=t.return;try{r.componentDidMount()}catch(u){K(t,s,u)}}var i=t.return;try{ti(t)}catch(u){K(t,i,u)}break;case 5:var o=t.return;try{ti(t)}catch(u){K(t,o,u)}}}catch(u){K(t,t.return,u)}if(t===e){L=null;break}var a=t.sibling;if(a!==null){a.return=t.return,L=a;break}L=t.return}}var lm=Math.ceil,xs=lt.ReactCurrentDispatcher,Zi=lt.ReactCurrentOwner,_e=lt.ReactCurrentBatchConfig,z=0,ne=null,X=null,le=0,Ne=0,un=bt(0),J=0,fr=null,Bt=0,Is=0,Ji=0,Gn=null,ge=null,eo=0,Nn=1/0,Ye=null,ys=!1,si=null,yt=null,zr=!1,ft=null,vs=0,Yn=0,li=null,Gr=-1,Yr=0;function fe(){return z&6?Y():Gr!==-1?Gr:Gr=Y()}function vt(e){return e.mode&1?z&2&&le!==0?le&-le:Vf.transition!==null?(Yr===0&&(Yr=cu()),Yr):(e=$,e!==0||(e=window.event,e=e===void 0?16:xu(e.type)),e):1}function Fe(e,t,n,r){if(50<Yn)throw Yn=0,li=null,Error(k(185));hr(e,n,r),(!(z&2)||e!==ne)&&(e===ne&&(!(z&2)&&(Is|=n),J===4&&ct(e,le)),we(e,r),n===1&&z===0&&!(t.mode&1)&&(Nn=Y()+500,Ds&&Et()))}function we(e,t){var n=e.callbackNode;Fd(e,t);var r=ns(e,e===ne?le:0);if(r===0)n!==null&&So(n),e.callbackNode=null,e.callbackPriority=0;else if(t=r&-r,e.callbackPriority!==t){if(n!=null&&So(n),t===1)e.tag===0?Ff(ha.bind(null,e)):zu(ha.bind(null,e)),Rf(function(){!(z&6)&&Et()}),n=null;else{switch(du(r)){case 1:n=bi;break;case 4:n=au;break;case 16:n=ts;break;case 536870912:n=uu;break;default:n=ts}n=Pc(n,Ec.bind(null,e))}e.callbackPriority=t,e.callbackNode=n}}function Ec(e,t){if(Gr=-1,Yr=0,z&6)throw Error(k(327));var n=e.callbackNode;if(hn()&&e.callbackNode!==n)return null;var r=ns(e,e===ne?le:0);if(r===0)return null;if(r&30||r&e.expiredLanes||t)t=ws(e,r);else{t=r;var s=z;z|=2;var i=Tc();(ne!==e||le!==t)&&(Ye=null,Nn=Y()+500,Ot(e,t));do try{am();break}catch(a){Lc(e,a)}while(1);Oi(),xs.current=i,z=s,X!==null?t=0:(ne=null,le=0,t=J)}if(t!==0){if(t===2&&(s=_l(e),s!==0&&(r=s,t=ii(e,s))),t===1)throw n=fr,Ot(e,0),ct(e,r),we(e,Y()),n;if(t===6)ct(e,r);else{if(s=e.current.alternate,!(r&30)&&!im(s)&&(t=ws(e,r),t===2&&(i=_l(e),i!==0&&(r=i,t=ii(e,i))),t===1))throw n=fr,Ot(e,0),ct(e,r),we(e,Y()),n;switch(e.finishedWork=s,e.finishedLanes=r,t){case 0:case 1:throw Error(k(345));case 2:At(e,ge,Ye);break;case 3:if(ct(e,r),(r&130023424)===r&&(t=eo+500-Y(),10<t)){if(ns(e,0)!==0)break;if(s=e.suspendedLanes,(s&r)!==r){fe(),e.pingedLanes|=e.suspendedLanes&s;break}e.timeoutHandle=Ul(At.bind(null,e,ge,Ye),t);break}At(e,ge,Ye);break;case 4:if(ct(e,r),(r&4194240)===r)break;for(t=e.eventTimes,s=-1;0<r;){var o=31-Ue(r);i=1<<o,o=t[o],o>s&&(s=o),r&=~i}if(r=s,r=Y()-r,r=(120>r?120:480>r?480:1080>r?1080:1920>r?1920:3e3>r?3e3:4320>r?4320:1960*lm(r/1960))-r,10<r){e.timeoutHandle=Ul(At.bind(null,e,ge,Ye),r);break}At(e,ge,Ye);break;case 5:At(e,ge,Ye);break;default:throw Error(k(329))}}}return we(e,Y()),e.callbackNode===n?Ec.bind(null,e):null}function ii(e,t){var n=Gn;return e.current.memoizedState.isDehydrated&&(Ot(e,t).flags|=256),e=ws(e,t),e!==2&&(t=ge,ge=n,t!==null&&oi(t)),e}function oi(e){ge===null?ge=e:ge.push.apply(ge,e)}function im(e){for(var t=e;;){if(t.flags&16384){var n=t.updateQueue;if(n!==null&&(n=n.stores,n!==null))for(var r=0;r<n.length;r++){var s=n[r],i=s.getSnapshot;s=s.value;try{if(!Ve(i(),s))return!1}catch{return!1}}}if(n=t.child,t.subtreeFlags&16384&&n!==null)n.return=t,t=n;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function ct(e,t){for(t&=~Ji,t&=~Is,e.suspendedLanes|=t,e.pingedLanes&=~t,e=e.expirationTimes;0<t;){var n=31-Ue(t),r=1<<n;e[n]=-1,t&=~r}}function ha(e){if(z&6)throw Error(k(327));hn();var t=ns(e,0);if(!(t&1))return we(e,Y()),null;var n=ws(e,t);if(e.tag!==0&&n===2){var r=_l(e);r!==0&&(t=r,n=ii(e,r))}if(n===1)throw n=fr,Ot(e,0),ct(e,t),we(e,Y()),n;if(n===6)throw Error(k(345));return e.finishedWork=e.current.alternate,e.finishedLanes=t,At(e,ge,Ye),we(e,Y()),null}function to(e,t){var n=z;z|=1;try{return e(t)}finally{z=n,z===0&&(Nn=Y()+500,Ds&&Et())}}function Wt(e){ft!==null&&ft.tag===0&&!(z&6)&&hn();var t=z;z|=1;var n=_e.transition,r=$;try{if(_e.transition=null,$=1,e)return e()}finally{$=r,_e.transition=n,z=t,!(z&6)&&Et()}}function no(){Ne=un.current,V(un)}function Ot(e,t){e.finishedWork=null,e.finishedLanes=0;var n=e.timeoutHandle;if(n!==-1&&(e.timeoutHandle=-1,zf(n)),X!==null)for(n=X.return;n!==null;){var r=n;switch(zi(r),r.tag){case 1:r=r.type.childContextTypes,r!=null&&os();break;case 3:wn(),V(ye),V(ce),Wi();break;case 5:Bi(r);break;case 4:wn();break;case 13:V(B);break;case 19:V(B);break;case 10:Ui(r.type._context);break;case 22:case 23:no()}n=n.return}if(ne=e,X=e=wt(e.current,null),le=Ne=t,J=0,fr=null,Ji=Is=Bt=0,ge=Gn=null,Rt!==null){for(t=0;t<Rt.length;t++)if(n=Rt[t],r=n.interleaved,r!==null){n.interleaved=null;var s=r.next,i=n.pending;if(i!==null){var o=i.next;i.next=s,r.next=o}n.pending=r}Rt=null}return e}function Lc(e,t){do{var n=X;try{if(Oi(),Wr.current=gs,hs){for(var r=W.memoizedState;r!==null;){var s=r.queue;s!==null&&(s.pending=null),r=r.next}hs=!1}if(Ht=0,te=Z=W=null,Qn=!1,ur=0,Zi.current=null,n===null||n.return===null){J=1,fr=t,X=null;break}e:{var i=e,o=n.return,a=n,u=t;if(t=le,a.flags|=32768,u!==null&&typeof u=="object"&&typeof u.then=="function"){var m=u,x=a,y=x.tag;if(!(x.mode&1)&&(y===0||y===11||y===15)){var v=x.alternate;v?(x.updateQueue=v.updateQueue,x.memoizedState=v.memoizedState,x.lanes=v.lanes):(x.updateQueue=null,x.memoizedState=null)}var w=na(o);if(w!==null){w.flags&=-257,ra(w,o,a,i,t),w.mode&1&&ta(i,m,t),t=w,u=m;var j=t.updateQueue;if(j===null){var p=new Set;p.add(u),t.updateQueue=p}else j.add(u);break e}else{if(!(t&1)){ta(i,m,t),ro();break e}u=Error(k(426))}}else if(H&&a.mode&1){var g=na(o);if(g!==null){!(g.flags&65536)&&(g.flags|=256),ra(g,o,a,i,t),Ri(jn(u,a));break e}}i=u=jn(u,a),J!==4&&(J=2),Gn===null?Gn=[i]:Gn.push(i),i=o;do{switch(i.tag){case 3:i.flags|=65536,t&=-t,i.lanes|=t;var c=dc(i,u,t);Yo(i,c);break e;case 1:a=u;var d=i.type,f=i.stateNode;if(!(i.flags&128)&&(typeof d.getDerivedStateFromError=="function"||f!==null&&typeof f.componentDidCatch=="function"&&(yt===null||!yt.has(f)))){i.flags|=65536,t&=-t,i.lanes|=t;var h=fc(i,a,t);Yo(i,h);break e}}i=i.return}while(i!==null)}Dc(n)}catch(N){t=N,X===n&&n!==null&&(X=n=n.return);continue}break}while(1)}function Tc(){var e=xs.current;return xs.current=gs,e===null?gs:e}function ro(){(J===0||J===3||J===2)&&(J=4),ne===null||!(Bt&268435455)&&!(Is&268435455)||ct(ne,le)}function ws(e,t){var n=z;z|=2;var r=Tc();(ne!==e||le!==t)&&(Ye=null,Ot(e,t));do try{om();break}catch(s){Lc(e,s)}while(1);if(Oi(),z=n,xs.current=r,X!==null)throw Error(k(261));return ne=null,le=0,J}function om(){for(;X!==null;)Mc(X)}function am(){for(;X!==null&&!_d();)Mc(X)}function Mc(e){var t=Ac(e.alternate,e,Ne);e.memoizedProps=e.pendingProps,t===null?Dc(e):X=t,Zi.current=null}function Dc(e){var t=e;do{var n=t.alternate;if(e=t.return,t.flags&32768){if(n=tm(n,t),n!==null){n.flags&=32767,X=n;return}if(e!==null)e.flags|=32768,e.subtreeFlags=0,e.deletions=null;else{J=6,X=null;return}}else if(n=em(n,t,Ne),n!==null){X=n;return}if(t=t.sibling,t!==null){X=t;return}X=t=e}while(t!==null);J===0&&(J=5)}function At(e,t,n){var r=$,s=_e.transition;try{_e.transition=null,$=1,um(e,t,n,r)}finally{_e.transition=s,$=r}return null}function um(e,t,n,r){do hn();while(ft!==null);if(z&6)throw Error(k(327));n=e.finishedWork;var s=e.finishedLanes;if(n===null)return null;if(e.finishedWork=null,e.finishedLanes=0,n===e.current)throw Error(k(177));e.callbackNode=null,e.callbackPriority=0;var i=n.lanes|n.childLanes;if(Vd(e,i),e===ne&&(X=ne=null,le=0),!(n.subtreeFlags&2064)&&!(n.flags&2064)||zr||(zr=!0,Pc(ts,function(){return hn(),null})),i=(n.flags&15990)!==0,n.subtreeFlags&15990||i){i=_e.transition,_e.transition=null;var o=$;$=1;var a=z;z|=4,Zi.current=null,rm(e,n),Cc(n,e),Tf($l),rs=!!Rl,$l=Rl=null,e.current=n,sm(n),Ad(),z=a,$=o,_e.transition=i}else e.current=n;if(zr&&(zr=!1,ft=e,vs=s),i=e.pendingLanes,i===0&&(yt=null),zd(n.stateNode),we(e,Y()),t!==null)for(r=e.onRecoverableError,n=0;n<t.length;n++)s=t[n],r(s.value,{componentStack:s.stack,digest:s.digest});if(ys)throw ys=!1,e=si,si=null,e;return vs&1&&e.tag!==0&&hn(),i=e.pendingLanes,i&1?e===li?Yn++:(Yn=0,li=e):Yn=0,Et(),null}function hn(){if(ft!==null){var e=du(vs),t=_e.transition,n=$;try{if(_e.transition=null,$=16>e?16:e,ft===null)var r=!1;else{if(e=ft,ft=null,vs=0,z&6)throw Error(k(331));var s=z;for(z|=4,L=e.current;L!==null;){var i=L,o=i.child;if(L.flags&16){var a=i.deletions;if(a!==null){for(var u=0;u<a.length;u++){var m=a[u];for(L=m;L!==null;){var x=L;switch(x.tag){case 0:case 11:case 15:Kn(8,x,i)}var y=x.child;if(y!==null)y.return=x,L=y;else for(;L!==null;){x=L;var v=x.sibling,w=x.return;if(Nc(x),x===m){L=null;break}if(v!==null){v.return=w,L=v;break}L=w}}}var j=i.alternate;if(j!==null){var p=j.child;if(p!==null){j.child=null;do{var g=p.sibling;p.sibling=null,p=g}while(p!==null)}}L=i}}if(i.subtreeFlags&2064&&o!==null)o.return=i,L=o;else e:for(;L!==null;){if(i=L,i.flags&2048)switch(i.tag){case 0:case 11:case 15:Kn(9,i,i.return)}var c=i.sibling;if(c!==null){c.return=i.return,L=c;break e}L=i.return}}var d=e.current;for(L=d;L!==null;){o=L;var f=o.child;if(o.subtreeFlags&2064&&f!==null)f.return=o,L=f;else e:for(o=d;L!==null;){if(a=L,a.flags&2048)try{switch(a.tag){case 0:case 11:case 15:Ps(9,a)}}catch(N){K(a,a.return,N)}if(a===o){L=null;break e}var h=a.sibling;if(h!==null){h.return=a.return,L=h;break e}L=a.return}}if(z=s,Et(),Qe&&typeof Qe.onPostCommitFiberRoot=="function")try{Qe.onPostCommitFiberRoot(bs,e)}catch{}r=!0}return r}finally{$=n,_e.transition=t}}return!1}function ga(e,t,n){t=jn(n,t),t=dc(e,t,1),e=xt(e,t,1),t=fe(),e!==null&&(hr(e,1,t),we(e,t))}function K(e,t,n){if(e.tag===3)ga(e,e,n);else for(;t!==null;){if(t.tag===3){ga(t,e,n);break}else if(t.tag===1){var r=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof r.componentDidCatch=="function"&&(yt===null||!yt.has(r))){e=jn(n,e),e=fc(t,e,1),t=xt(t,e,1),e=fe(),t!==null&&(hr(t,1,e),we(t,e));break}}t=t.return}}function cm(e,t,n){var r=e.pingCache;r!==null&&r.delete(t),t=fe(),e.pingedLanes|=e.suspendedLanes&n,ne===e&&(le&n)===n&&(J===4||J===3&&(le&130023424)===le&&500>Y()-eo?Ot(e,0):Ji|=n),we(e,t)}function _c(e,t){t===0&&(e.mode&1?(t=br,br<<=1,!(br&130023424)&&(br=4194304)):t=1);var n=fe();e=rt(e,t),e!==null&&(hr(e,t,n),we(e,n))}function dm(e){var t=e.memoizedState,n=0;t!==null&&(n=t.retryLane),_c(e,n)}function fm(e,t){var n=0;switch(e.tag){case 13:var r=e.stateNode,s=e.memoizedState;s!==null&&(n=s.retryLane);break;case 19:r=e.stateNode;break;default:throw Error(k(314))}r!==null&&r.delete(t),_c(e,n)}var Ac;Ac=function(e,t,n){if(e!==null)if(e.memoizedProps!==t.pendingProps||ye.current)xe=!0;else{if(!(e.lanes&n)&&!(t.flags&128))return xe=!1,Jf(e,t,n);xe=!!(e.flags&131072)}else xe=!1,H&&t.flags&1048576&&Ru(t,cs,t.index);switch(t.lanes=0,t.tag){case 2:var r=t.type;Kr(e,t),e=t.pendingProps;var s=xn(t,ce.current);pn(t,n),s=Ki(null,t,r,e,s,n);var i=Gi();return t.flags|=1,typeof s=="object"&&s!==null&&typeof s.render=="function"&&s.$$typeof===void 0?(t.tag=1,t.memoizedState=null,t.updateQueue=null,ve(r)?(i=!0,as(t)):i=!1,t.memoizedState=s.state!==null&&s.state!==void 0?s.state:null,Vi(t),s.updater=As,t.stateNode=s,s._reactInternals=t,Kl(t,r,e,n),t=Xl(null,t,r,!0,i,n)):(t.tag=0,H&&i&&Ii(t),de(null,t,s,n),t=t.child),t;case 16:r=t.elementType;e:{switch(Kr(e,t),e=t.pendingProps,s=r._init,r=s(r._payload),t.type=r,s=t.tag=pm(r),e=Re(r,e),s){case 0:t=Yl(null,t,r,e,n);break e;case 1:t=ia(null,t,r,e,n);break e;case 11:t=sa(null,t,r,e,n);break e;case 14:t=la(null,t,r,Re(r.type,e),n);break e}throw Error(k(306,r,""))}return t;case 0:return r=t.type,s=t.pendingProps,s=t.elementType===r?s:Re(r,s),Yl(e,t,r,s,n);case 1:return r=t.type,s=t.pendingProps,s=t.elementType===r?s:Re(r,s),ia(e,t,r,s,n);case 3:e:{if(gc(t),e===null)throw Error(k(387));r=t.pendingProps,i=t.memoizedState,s=i.element,Hu(e,t),ms(t,r,null,n);var o=t.memoizedState;if(r=o.element,i.isDehydrated)if(i={element:r,isDehydrated:!1,cache:o.cache,pendingSuspenseBoundaries:o.pendingSuspenseBoundaries,transitions:o.transitions},t.updateQueue.baseState=i,t.memoizedState=i,t.flags&256){s=jn(Error(k(423)),t),t=oa(e,t,r,n,s);break e}else if(r!==s){s=jn(Error(k(424)),t),t=oa(e,t,r,n,s);break e}else for(Se=gt(t.stateNode.containerInfo.firstChild),ke=t,H=!0,Oe=null,n=Fu(t,null,r,n),t.child=n;n;)n.flags=n.flags&-3|4096,n=n.sibling;else{if(yn(),r===s){t=st(e,t,n);break e}de(e,t,r,n)}t=t.child}return t;case 5:return Bu(t),e===null&&Bl(t),r=t.type,s=t.pendingProps,i=e!==null?e.memoizedProps:null,o=s.children,Ol(r,s)?o=null:i!==null&&Ol(r,i)&&(t.flags|=32),hc(e,t),de(e,t,o,n),t.child;case 6:return e===null&&Bl(t),null;case 13:return xc(e,t,n);case 4:return Hi(t,t.stateNode.containerInfo),r=t.pendingProps,e===null?t.child=vn(t,null,r,n):de(e,t,r,n),t.child;case 11:return r=t.type,s=t.pendingProps,s=t.elementType===r?s:Re(r,s),sa(e,t,r,s,n);case 7:return de(e,t,t.pendingProps,n),t.child;case 8:return de(e,t,t.pendingProps.children,n),t.child;case 12:return de(e,t,t.pendingProps.children,n),t.child;case 10:e:{if(r=t.type._context,s=t.pendingProps,i=t.memoizedProps,o=s.value,U(ds,r._currentValue),r._currentValue=o,i!==null)if(Ve(i.value,o)){if(i.children===s.children&&!ye.current){t=st(e,t,n);break e}}else for(i=t.child,i!==null&&(i.return=t);i!==null;){var a=i.dependencies;if(a!==null){o=i.child;for(var u=a.firstContext;u!==null;){if(u.context===r){if(i.tag===1){u=Je(-1,n&-n),u.tag=2;var m=i.updateQueue;if(m!==null){m=m.shared;var x=m.pending;x===null?u.next=u:(u.next=x.next,x.next=u),m.pending=u}}i.lanes|=n,u=i.alternate,u!==null&&(u.lanes|=n),Wl(i.return,n,t),a.lanes|=n;break}u=u.next}}else if(i.tag===10)o=i.type===t.type?null:i.child;else if(i.tag===18){if(o=i.return,o===null)throw Error(k(341));o.lanes|=n,a=o.alternate,a!==null&&(a.lanes|=n),Wl(o,n,t),o=i.sibling}else o=i.child;if(o!==null)o.return=i;else for(o=i;o!==null;){if(o===t){o=null;break}if(i=o.sibling,i!==null){i.return=o.return,o=i;break}o=o.return}i=o}de(e,t,s.children,n),t=t.child}return t;case 9:return s=t.type,r=t.pendingProps.children,pn(t,n),s=Ae(s),r=r(s),t.flags|=1,de(e,t,r,n),t.child;case 14:return r=t.type,s=Re(r,t.pendingProps),s=Re(r.type,s),la(e,t,r,s,n);case 15:return mc(e,t,t.type,t.pendingProps,n);case 17:return r=t.type,s=t.pendingProps,s=t.elementType===r?s:Re(r,s),Kr(e,t),t.tag=1,ve(r)?(e=!0,as(t)):e=!1,pn(t,n),cc(t,r,s),Kl(t,r,s,n),Xl(null,t,r,!0,e,n);case 19:return yc(e,t,n);case 22:return pc(e,t,n)}throw Error(k(156,t.tag))};function Pc(e,t){return ou(e,t)}function mm(e,t,n,r){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function De(e,t,n,r){return new mm(e,t,n,r)}function so(e){return e=e.prototype,!(!e||!e.isReactComponent)}function pm(e){if(typeof e=="function")return so(e)?1:0;if(e!=null){if(e=e.$$typeof,e===Si)return 11;if(e===ki)return 14}return 2}function wt(e,t){var n=e.alternate;return n===null?(n=De(e.tag,t,e.key,e.mode),n.elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=e.flags&14680064,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n}function Xr(e,t,n,r,s,i){var o=2;if(r=e,typeof e=="function")so(e)&&(o=1);else if(typeof e=="string")o=5;else e:switch(e){case Zt:return Ut(n.children,s,i,t);case Ni:o=8,s|=8;break;case xl:return e=De(12,n,t,s|2),e.elementType=xl,e.lanes=i,e;case yl:return e=De(13,n,t,s),e.elementType=yl,e.lanes=i,e;case vl:return e=De(19,n,t,s),e.elementType=vl,e.lanes=i,e;case Ba:return zs(n,s,i,t);default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case Va:o=10;break e;case Ha:o=9;break e;case Si:o=11;break e;case ki:o=14;break e;case ot:o=16,r=null;break e}throw Error(k(130,e==null?e:typeof e,""))}return t=De(o,n,t,s),t.elementType=e,t.type=r,t.lanes=i,t}function Ut(e,t,n,r){return e=De(7,e,r,t),e.lanes=n,e}function zs(e,t,n,r){return e=De(22,e,r,t),e.elementType=Ba,e.lanes=n,e.stateNode={isHidden:!1},e}function fl(e,t,n){return e=De(6,e,null,t),e.lanes=n,e}function ml(e,t,n){return t=De(4,e.children!==null?e.children:[],e.key,t),t.lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function hm(e,t,n,r,s){this.tag=t,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=Ks(0),this.expirationTimes=Ks(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=Ks(0),this.identifierPrefix=r,this.onRecoverableError=s,this.mutableSourceEagerHydrationData=null}function lo(e,t,n,r,s,i,o,a,u){return e=new hm(e,t,n,a,u),t===1?(t=1,i===!0&&(t|=8)):t=0,i=De(3,null,null,t),e.current=i,i.stateNode=e,i.memoizedState={element:r,isDehydrated:n,cache:null,transitions:null,pendingSuspenseBoundaries:null},Vi(i),e}function gm(e,t,n){var r=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:qt,key:r==null?null:""+r,children:e,containerInfo:t,implementation:n}}function Ic(e){if(!e)return kt;e=e._reactInternals;e:{if(Gt(e)!==e||e.tag!==1)throw Error(k(170));var t=e;do{switch(t.tag){case 3:t=t.stateNode.context;break e;case 1:if(ve(t.type)){t=t.stateNode.__reactInternalMemoizedMergedChildContext;break e}}t=t.return}while(t!==null);throw Error(k(171))}if(e.tag===1){var n=e.type;if(ve(n))return Iu(e,n,t)}return t}function zc(e,t,n,r,s,i,o,a,u){return e=lo(n,r,!0,e,s,i,o,a,u),e.context=Ic(null),n=e.current,r=fe(),s=vt(n),i=Je(r,s),i.callback=t??null,xt(n,i,s),e.current.lanes=s,hr(e,s,r),we(e,r),e}function Rs(e,t,n,r){var s=t.current,i=fe(),o=vt(s);return n=Ic(n),t.context===null?t.context=n:t.pendingContext=n,t=Je(i,o),t.payload={element:e},r=r===void 0?null:r,r!==null&&(t.callback=r),e=xt(s,t,o),e!==null&&(Fe(e,s,o,i),Br(e,s,o)),o}function js(e){if(e=e.current,!e.child)return null;switch(e.child.tag){case 5:return e.child.stateNode;default:return e.child.stateNode}}function xa(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var n=e.retryLane;e.retryLane=n!==0&&n<t?n:t}}function io(e,t){xa(e,t),(e=e.alternate)&&xa(e,t)}function xm(){return null}var Rc=typeof reportError=="function"?reportError:function(e){console.error(e)};function oo(e){this._internalRoot=e}$s.prototype.render=oo.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(k(409));Rs(e,t,null,null)};$s.prototype.unmount=oo.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;Wt(function(){Rs(null,e,null,null)}),t[nt]=null}};function $s(e){this._internalRoot=e}$s.prototype.unstable_scheduleHydration=function(e){if(e){var t=pu();e={blockedOn:null,target:e,priority:t};for(var n=0;n<ut.length&&t!==0&&t<ut[n].priority;n++);ut.splice(n,0,e),n===0&&gu(e)}};function ao(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function Os(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11&&(e.nodeType!==8||e.nodeValue!==" react-mount-point-unstable "))}function ya(){}function ym(e,t,n,r,s){if(s){if(typeof r=="function"){var i=r;r=function(){var m=js(o);i.call(m)}}var o=zc(t,r,e,0,null,!1,!1,"",ya);return e._reactRootContainer=o,e[nt]=o.current,sr(e.nodeType===8?e.parentNode:e),Wt(),o}for(;s=e.lastChild;)e.removeChild(s);if(typeof r=="function"){var a=r;r=function(){var m=js(u);a.call(m)}}var u=lo(e,0,!1,null,null,!1,!1,"",ya);return e._reactRootContainer=u,e[nt]=u.current,sr(e.nodeType===8?e.parentNode:e),Wt(function(){Rs(t,u,n,r)}),u}function Us(e,t,n,r,s){var i=n._reactRootContainer;if(i){var o=i;if(typeof s=="function"){var a=s;s=function(){var u=js(o);a.call(u)}}Rs(t,o,e,s)}else o=ym(n,t,e,s,r);return js(o)}fu=function(e){switch(e.tag){case 3:var t=e.stateNode;if(t.current.memoizedState.isDehydrated){var n=On(t.pendingLanes);n!==0&&(Ei(t,n|1),we(t,Y()),!(z&6)&&(Nn=Y()+500,Et()))}break;case 13:Wt(function(){var r=rt(e,1);if(r!==null){var s=fe();Fe(r,e,1,s)}}),io(e,1)}};Li=function(e){if(e.tag===13){var t=rt(e,134217728);if(t!==null){var n=fe();Fe(t,e,134217728,n)}io(e,134217728)}};mu=function(e){if(e.tag===13){var t=vt(e),n=rt(e,t);if(n!==null){var r=fe();Fe(n,e,t,r)}io(e,t)}};pu=function(){return $};hu=function(e,t){var n=$;try{return $=e,t()}finally{$=n}};Tl=function(e,t,n){switch(t){case"input":if(Nl(e,n),t=n.name,n.type==="radio"&&t!=null){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll("input[name="+JSON.stringify(""+t)+'][type="radio"]'),t=0;t<n.length;t++){var r=n[t];if(r!==e&&r.form===e.form){var s=Ms(r);if(!s)throw Error(k(90));Qa(r),Nl(r,s)}}}break;case"textarea":Ga(e,n);break;case"select":t=n.value,t!=null&&cn(e,!!n.multiple,t,!1)}};tu=to;nu=Wt;var vm={usingClientEntryPoint:!1,Events:[xr,nn,Ms,Ja,eu,to]},zn={findFiberByHostInstance:zt,bundleType:0,version:"18.3.1",rendererPackageName:"react-dom"},wm={bundleType:zn.bundleType,version:zn.version,rendererPackageName:zn.rendererPackageName,rendererConfig:zn.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:lt.ReactCurrentDispatcher,findHostInstanceByFiber:function(e){return e=lu(e),e===null?null:e.stateNode},findFiberByHostInstance:zn.findFiberByHostInstance||xm,findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.3.1-next-f1338f8080-20240426"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var Rr=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!Rr.isDisabled&&Rr.supportsFiber)try{bs=Rr.inject(wm),Qe=Rr}catch{}}be.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=vm;be.createPortal=function(e,t){var n=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!ao(t))throw Error(k(200));return gm(e,t,null,n)};be.createRoot=function(e,t){if(!ao(e))throw Error(k(299));var n=!1,r="",s=Rc;return t!=null&&(t.unstable_strictMode===!0&&(n=!0),t.identifierPrefix!==void 0&&(r=t.identifierPrefix),t.onRecoverableError!==void 0&&(s=t.onRecoverableError)),t=lo(e,1,!1,null,null,n,!1,r,s),e[nt]=t.current,sr(e.nodeType===8?e.parentNode:e),new oo(t)};be.findDOMNode=function(e){if(e==null)return null;if(e.nodeType===1)return e;var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(k(188)):(e=Object.keys(e).join(","),Error(k(268,e)));return e=lu(t),e=e===null?null:e.stateNode,e};be.flushSync=function(e){return Wt(e)};be.hydrate=function(e,t,n){if(!Os(t))throw Error(k(200));return Us(null,e,t,!0,n)};be.hydrateRoot=function(e,t,n){if(!ao(e))throw Error(k(405));var r=n!=null&&n.hydratedSources||null,s=!1,i="",o=Rc;if(n!=null&&(n.unstable_strictMode===!0&&(s=!0),n.identifierPrefix!==void 0&&(i=n.identifierPrefix),n.onRecoverableError!==void 0&&(o=n.onRecoverableError)),t=zc(t,null,e,1,n??null,s,!1,i,o),e[nt]=t.current,sr(e),r)for(e=0;e<r.length;e++)n=r[e],s=n._getVersion,s=s(n._source),t.mutableSourceEagerHydrationData==null?t.mutableSourceEagerHydrationData=[n,s]:t.mutableSourceEagerHydrationData.push(n,s);return new $s(t)};be.render=function(e,t,n){if(!Os(t))throw Error(k(200));return Us(null,e,t,!1,n)};be.unmountComponentAtNode=function(e){if(!Os(e))throw Error(k(40));return e._reactRootContainer?(Wt(function(){Us(null,null,e,!1,function(){e._reactRootContainer=null,e[nt]=null})}),!0):!1};be.unstable_batchedUpdates=to;be.unstable_renderSubtreeIntoContainer=function(e,t,n,r){if(!Os(n))throw Error(k(200));if(e==null||e._reactInternals===void 0)throw Error(k(38));return Us(e,t,n,!1,r)};be.version="18.3.1-next-f1338f8080-20240426";function $c(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE($c)}catch(e){console.error(e)}}$c(),$a.exports=be;var jm=$a.exports,va=jm;hl.createRoot=va.createRoot,hl.hydrateRoot=va.hydrateRoot;const pl="http://localhost:5000/api";class Nm{constructor(t){Le(this,"baseURL");this.baseURL=t}async request(t,n={}){const r=`${this.baseURL}${t}`,s=new AbortController,i=setTimeout(()=>s.abort(),1e4),o={headers:{"Content-Type":"application/json",...n.headers},signal:s.signal,...n};try{const a=await fetch(r,o);if(clearTimeout(i),!a.ok){const u=await a.json().catch(()=>({}));throw new Error(u.error||`HTTP ${a.status}: ${a.statusText}`)}return await a.json()}catch(a){throw clearTimeout(i),console.error(`API request failed: ${r}`,a),a}}async get(t){return this.request(t,{method:"GET"})}async post(t,n){return this.request(t,{method:"POST",body:n?JSON.stringify(n):void 0})}async put(t,n){return this.request(t,{method:"PUT",body:n?JSON.stringify(n):void 0})}async delete(t){return this.request(t,{method:"DELETE"})}}class Sm{constructor(){Le(this,"client");Le(this,"isOnline",!0);Le(this,"connectionPromise",null);this.client=new Nm(pl)}async checkConnection(){if(this.connectionPromise)return console.log("[ApiService] Connection check already in progress, reusing promise"),this.connectionPromise;this.connectionPromise=this._performConnectionCheck();try{return await this.connectionPromise}finally{this.connectionPromise=null}}async _performConnectionCheck(){try{const n=`${pl.replace("/api","")}/health`;console.log(`[ApiService] Checking connection to: ${n}`);const r=new AbortController;let s=null;try{s=setTimeout(()=>{console.log("[ApiService] Connection check timeout, aborting request"),r.abort()},8e3);const i=await fetch(n,{signal:r.signal,method:"GET",cache:"no-cache",headers:{"Cache-Control":"no-cache"}});return s&&(clearTimeout(s),s=null),console.log(`[ApiService] Response status: ${i.status}`),i.ok?(this.isOnline=!0,console.log("[ApiService] Connection check successful"),!0):(this.isOnline=!1,console.warn(`[ApiService] Connection check failed with status: ${i.status}`),!1)}catch(i){throw s&&clearTimeout(s),i}}catch(t){return console.warn("[ApiService] Backend server is not available:",t),t instanceof Error&&(t.name==="AbortError"?console.warn("[ApiService] Request was aborted (likely due to timeout)"):t.name==="TypeError"&&t.message.includes("fetch")?console.warn("[ApiService] Network error or server not reachable"):console.warn(`[ApiService] Unexpected error: ${t.name} - ${t.message}`)),this.isOnline=!1,!1}}getConnectionStatus(){return this.isOnline}async getLLMConfigs(){return this.client.get("/llm-configs")}async createLLMConfig(t){return this.client.post("/llm-configs",t)}async updateLLMConfig(t,n){return this.client.put(`/llm-configs/${t}`,n)}async deleteLLMConfig(t){try{await this.client.delete(`/llm-configs/${t}`)}catch(n){throw n instanceof Error&&n.message.includes("无法删除正在使用的LLM配置")?new Error("无法删除正在使用的LLM配置，请先从智能体中移除此配置"):n}}async getAgents(){return this.client.get("/agents")}async createAgent(t){return this.client.post("/agents",t)}async updateAgent(t,n){return this.client.put(`/agents/${t}`,n)}async deleteAgent(t){await this.client.delete(`/agents/${t}`)}async getDiscussions(){return this.client.get("/discussions")}async createDiscussion(t){return this.client.post("/discussions",t)}async updateDiscussion(t,n){return this.client.put(`/discussions/${t}`,n)}async deleteDiscussion(t){await this.client.delete(`/discussions/${t}`)}async addMessage(t,n){return this.client.post(`/discussions/${t}/messages`,n)}async getSettings(){return this.client.get("/settings")}async updateSettings(t){return this.client.put("/settings",t)}async getPreferences(){return this.client.get("/preferences")}async updatePreferences(t){return this.client.put("/preferences",t)}async exportData(){return this.client.get("/data/export")}async importData(t,n=!1){await this.client.post("/data/import",{...t,clearExisting:n})}async clearAllData(){await this.client.delete("/data/clear")}async getStorageInfo(){return this.client.get("/storage/info")}}const O=new Sm,Pt=class Pt{constructor(){Le(this,"isInitialized",!1);Le(this,"storageMode","server");Le(this,"serverAvailable",!1)}static getInstance(){return Pt.instance||(Pt.instance=new Pt),Pt.instance}async initialize(){if(this.isInitialized)return;console.log("Starting StorageService initialization..."),console.log("Checking server connection..."),console.log("API_BASE_URL: http://localhost:5000/api");let t=0;const n=3;for(;t<n&&!this.serverAvailable;){t++,console.log(`[StorageService] Connection attempt ${t}/${n}`);try{if(this.serverAvailable=await O.checkConnection(),console.log(`[StorageService] Connection check result: ${this.serverAvailable}`),this.serverAvailable)break;t<n&&(console.log("[StorageService] Waiting 2 seconds before retry..."),await new Promise(r=>setTimeout(r,2e3)))}catch(r){console.error(`Server connection check failed (attempt ${t}):`,r),this.serverAvailable=!1,t<n&&(console.log("[StorageService] Waiting 2 seconds before retry..."),await new Promise(s=>setTimeout(s,2e3)))}}if(console.log(`Server available: ${this.serverAvailable}`),!this.serverAvailable)throw new Error("服务器连接失败，请确保后端服务正在运行");await this.determineStorageMode(),console.log(`Storage mode: ${this.storageMode}`),await this.initializeDefaultSettings(),console.log("Default settings initialized"),await this.migrateData(),console.log("Data migration completed"),this.isInitialized=!0,console.log(`StorageService initialized successfully with ${this.storageMode} mode`)}async determineStorageMode(){this.storageMode="server"}setStorageMode(t){this.storageMode=t}getStorageMode(){return this.storageMode}isServerAvailable(){return this.serverAvailable}async refreshServerConnection(){return this.serverAvailable=await O.checkConnection(),this.serverAvailable}async initializeDefaultSettings(){const t={version:"1.0.0",lastUpdated:new Date().toISOString(),autoSave:!0,maxStoredDiscussions:100,defaultDiscussionMode:"free",theme:"light"},n={defaultAgentCount:3,preferredLLMProvider:"openai",autoStartDiscussion:!1,showAdvancedOptions:!1,notificationsEnabled:!0,exportFormat:"json"};this.getSettings()||this.saveSettings(t),this.getPreferences()||this.savePreferences(n)}async migrateData(){const t=await this.getSettings();((t==null?void 0:t.version)||"0.0.0")<"1.0.0"&&console.log("Migrating data to version 1.0.0...")}async saveAgents(t){try{if(this.serverAvailable){console.log(`[StorageService] Starting batch save for ${t.length} agents`);const n=await this.getAgents(),r=t.map(async s=>n.findIndex(o=>o.id===s.id)>=0?O.updateAgent(s.id,s):O.createAgent(s));await Promise.all(r),console.log(`[StorageService] Successfully saved ${t.length} agents to server`)}else throw new Error("服务器不可用，无法保存智能体数据");this.updateLastModified()}catch(n){throw console.error("Failed to save agents:",n),new Error("Failed to save agents to storage")}}async getAgents(){if(this.serverAvailable)return await O.getAgents();throw new Error("服务器不可用，无法获取智能体数据")}async saveAgent(t){if(this.serverAvailable)(await this.getAgents()).findIndex(s=>s.id===t.id)>=0?await O.updateAgent(t.id,t):await O.createAgent(t);else throw new Error("服务器不可用，无法保存智能体")}async deleteAgent(t){if(this.serverAvailable)await O.deleteAgent(t);else throw new Error("服务器不可用，无法删除智能体")}async saveLLMConfigs(t){console.log(`[StorageService] Starting saveLLMConfigs with ${t.length} configs`),console.log(`[StorageService] Storage mode: ${this.storageMode}, Server available: ${this.serverAvailable}`);try{if(this.serverAvailable){console.log("[StorageService] Attempting to save LLM configs to server...");const n=await this.getLLMConfigs(),r=t.map(async s=>n.findIndex(o=>o.id===s.id)>=0?O.updateLLMConfig(s.id,s):O.createLLMConfig(s));await Promise.all(r),console.log(`[StorageService] Successfully saved ${t.length} LLM configs to server`)}else throw new Error("服务器不可用，无法保存LLM配置");this.updateLastModified(),console.log("[StorageService] saveLLMConfigs completed successfully")}catch(n){throw console.error("[StorageService] Failed to save LLM configs:",n),new Error("Failed to save LLM configs to storage")}}async getLLMConfigs(){if(this.serverAvailable){console.log("[StorageService] Using SERVER mode - attempting to load from server");const t=await O.getLLMConfigs();return console.log(`[StorageService] Successfully loaded ${t.length} configs from server`),t}else throw new Error("服务器不可用，无法获取LLM配置数据")}async saveLLMConfig(t){if(console.log(`[StorageService] Starting saveLLMConfig for config: ${t.id} (${t.name})`),console.log(`[StorageService] Storage mode: ${this.storageMode}, Server available: ${this.serverAvailable}`),this.serverAvailable){console.log("[StorageService] Attempting to save single config to server...");const r=(await this.getLLMConfigs()).findIndex(s=>s.id===t.id);console.log(`[StorageService] Existing config index: ${r}`),r>=0?(await O.updateLLMConfig(t.id,t),console.log("[StorageService] Config updated on server successfully")):(await O.createLLMConfig(t),console.log("[StorageService] Config created on server successfully")),console.log("[StorageService] saveLLMConfig completed successfully")}else throw new Error("服务器不可用，无法保存LLM配置")}async deleteLLMConfig(t){if(console.log(`[StorageService] Starting deleteLLMConfig for config: ${t}`),console.log(`[StorageService] Storage mode: ${this.storageMode}, Server available: ${this.serverAvailable}`),this.serverAvailable)console.log("[StorageService] Attempting to delete config from server..."),await O.deleteLLMConfig(t),console.log("[StorageService] Config deleted from server successfully"),console.log("[StorageService] deleteLLMConfig completed successfully");else throw new Error("服务器不可用，无法删除LLM配置")}async exportLLMConfigs(){try{const n=(await this.getLLMConfigs()).map(r=>({...r,apiKey:"***HIDDEN***"}));return JSON.stringify(n,null,2)}catch(t){throw console.error("Failed to export LLM configs:",t),new Error("导出LLM配置失败")}}async importLLMConfigs(t){const n={success:0,errors:[]};try{const r=JSON.parse(t);if(!Array.isArray(r))throw new Error("数据格式错误：应该是配置数组");for(const s of r)try{if(!s.id||!s.name||!s.provider||!s.model){n.errors.push(`配置 ${s.name||"Unknown"} 缺少必要字段`);continue}if(s.apiKey==="***HIDDEN***"){n.errors.push(`配置 ${s.name} 的API密钥需要重新设置`);continue}await this.saveLLMConfig(s),n.success++}catch(i){n.errors.push(`导入配置 ${s.name||"Unknown"} 失败: ${i instanceof Error?i.message:"未知错误"}`)}}catch(r){n.errors.push("解析JSON数据失败: "+(r instanceof Error?r.message:"未知错误"))}return n}generateLLMConfigId(){return`llm_${Date.now()}_${Math.random().toString(36).substring(2,11)}`}createDefaultLLMConfig(t,n,r){return{id:this.generateLLMConfigId(),name:t.name,provider:t.provider.toLowerCase(),model:t.model,apiKey:n,baseURL:r,temperature:t.defaultSettings.temperature,maxTokens:t.defaultSettings.maxTokens}}async getLLMConfig(t){try{return(await this.getLLMConfigs()).find(r=>r.id===t)||null}catch(n){return console.error("Failed to get LLM config:",n),null}}validateLLMConfig(t){var r,s,i,o;const n=[];return(r=t.name)!=null&&r.trim()||n.push("配置名称不能为空"),t.provider||n.push("请选择提供商"),(s=t.model)!=null&&s.trim()||n.push("模型名称不能为空"),(i=t.apiKey)!=null&&i.trim()||n.push("API密钥不能为空"),t.temperature!==void 0&&(t.temperature<0||t.temperature>2)&&n.push("温度值应在0-2之间"),t.maxTokens!==void 0&&(t.maxTokens<1||t.maxTokens>4e3)&&n.push("最大令牌数应在1-4000之间"),t.provider==="azure"&&!((o=t.baseURL)!=null&&o.trim())&&n.push("Azure提供商需要设置基础URL"),n}async getLLMConfigStats(){try{const t=await this.getLLMConfigs(),n={};t.forEach(s=>{n[s.provider]=(n[s.provider]||0)+1});const r=t.slice(0,5);return{total:t.length,byProvider:n,recentlyUsed:r}}catch(t){return console.error("Failed to get LLM config stats:",t),{total:0,byProvider:{},recentlyUsed:[]}}}async saveDiscussions(t){try{const n=await this.getSettings(),r=(n==null?void 0:n.maxStoredDiscussions)||100,s=t.sort((i,o)=>new Date(o.createdAt).getTime()-new Date(i.createdAt).getTime()).slice(0,r);if(this.serverAvailable){console.log(`[StorageService] Starting batch save for ${s.length} discussions`);const i=await this.getDiscussions(),o=s.map(async a=>i.findIndex(m=>m.id===a.id)>=0?O.updateDiscussion(a.id,a):O.createDiscussion(a));await Promise.all(o),console.log(`[StorageService] Successfully saved ${s.length} discussions to server`)}else throw new Error("服务器不可用，无法保存讨论数据");this.updateLastModified()}catch(n){throw console.error("Failed to save discussions:",n),new Error("Failed to save discussions to storage")}}async getDiscussions(){if(this.serverAvailable)return await O.getDiscussions();throw new Error("服务器不可用，无法获取讨论数据")}async saveDiscussion(t){try{if(this.serverAvailable)(await this.getDiscussions()).findIndex(s=>s.id===t.id)>=0?await O.updateDiscussion(t.id,t):await O.createDiscussion(t);else throw new Error("服务器不可用，无法保存讨论")}catch(n){throw console.error("Failed to save discussion:",n),n}}async deleteDiscussion(t){try{if(this.serverAvailable)await O.deleteDiscussion(t);else throw new Error("服务器不可用，无法删除讨论")}catch(n){throw console.error("Failed to delete discussion:",n),n}}async saveSettings(t){if(this.serverAvailable)await O.updateSettings(t);else throw new Error("服务器不可用，无法保存设置")}async getSettings(){if(this.serverAvailable)return await O.getSettings();throw new Error("服务器不可用，无法获取设置数据")}async savePreferences(t){try{if(this.serverAvailable)await O.updatePreferences(t);else throw new Error("服务器不可用，无法保存用户偏好")}catch(n){throw console.error("Failed to save preferences:",n),new Error("Failed to save preferences to storage")}}async getPreferences(){try{if(this.serverAvailable)return await O.getPreferences();throw new Error("服务器不可用，无法获取用户偏好数据")}catch(t){throw console.error("Failed to load preferences:",t),t}}async getAllData(){return{agents:await this.getAgents(),llmConfigs:await this.getLLMConfigs(),discussions:await this.getDiscussions(),settings:await this.getSettings()||{},preferences:await this.getPreferences()||{}}}async importAllData(t){if(this.serverAvailable)await O.importData(t,!1);else throw new Error("服务器不可用，无法导入数据")}async clearAllData(){if(this.serverAvailable)await O.clearAllData();else throw new Error("服务器不可用，无法清除数据")}async updateLastModified(){const t=await this.getSettings();t&&(t.lastUpdated=new Date().toISOString(),await this.saveSettings(t))}validateData(t){try{return!(t.agents&&!Array.isArray(t.agents)||t.llmConfigs&&!Array.isArray(t.llmConfigs)||t.discussions&&!Array.isArray(t.discussions))}catch{return!1}}async getStorageInfo(){if(this.serverAvailable)return await O.getStorageInfo();throw new Error("服务器不可用，无法获取存储信息")}};Le(Pt,"instance");let ai=Pt;const R=ai.getInstance();let $r;const km=new Uint8Array(16);function Cm(){if(!$r&&($r=typeof crypto<"u"&&crypto.getRandomValues&&crypto.getRandomValues.bind(crypto),!$r))throw new Error("crypto.getRandomValues() not supported. See https://github.com/uuidjs/uuid#getrandomvalues-not-supported");return $r(km)}const re=[];for(let e=0;e<256;++e)re.push((e+256).toString(16).slice(1));function bm(e,t=0){return re[e[t+0]]+re[e[t+1]]+re[e[t+2]]+re[e[t+3]]+"-"+re[e[t+4]]+re[e[t+5]]+"-"+re[e[t+6]]+re[e[t+7]]+"-"+re[e[t+8]]+re[e[t+9]]+"-"+re[e[t+10]]+re[e[t+11]]+re[e[t+12]]+re[e[t+13]]+re[e[t+14]]+re[e[t+15]]}const Em=typeof crypto<"u"&&crypto.randomUUID&&crypto.randomUUID.bind(crypto),wa={randomUUID:Em};function ui(e,t,n){if(wa.randomUUID&&!t&&!e)return wa.randomUUID();e=e||{};const r=e.random||(e.rng||Cm)();if(r[6]=r[6]&15|64,r[8]=r[8]&63|128,t){n=n||0;for(let s=0;s<16;++s)t[n+s]=r[s];return t}return bm(r)}const Oc={agents:[],currentDiscussion:null,allDiscussions:[],isDiscussionActive:!1,settings:null,preferences:null,isLoading:!0,loadingStep:"storage"},Uc=T.createContext({state:Oc,dispatch:()=>null,addAgent:()=>null,updateAgent:()=>null,deleteAgent:()=>null,startDiscussion:()=>null,endDiscussion:()=>null,sendMessage:()=>null,updateSettings:()=>null,updatePreferences:()=>null,exportData:async()=>"",importData:async()=>!1,clearAllData:async()=>{}});function Lm(e,t){switch(t.type){case"ADD_AGENT":return{...e,agents:[...e.agents,t.payload]};case"UPDATE_AGENT":return{...e,agents:e.agents.map(o=>o.id===t.payload.id?t.payload:o)};case"DELETE_AGENT":return{...e,agents:e.agents.filter(o=>o.id!==t.payload)};case"START_DISCUSSION":const n={id:ui(),topic:t.payload.topic,mode:t.payload.mode,participants:t.payload.selectedAgents,messages:[],status:"active",consensus:null,createdAt:new Date,consensusScore:0,moderatorId:t.payload.moderatorId,moderatorSummaries:[],topicRelevanceScore:1,moderatorInterventions:0};return{...e,currentDiscussion:n,isDiscussionActive:!0};case"ADD_MESSAGE":if(!e.currentDiscussion)return e;const r={...e.currentDiscussion,messages:[...e.currentDiscussion.messages,t.payload]};return{...e,currentDiscussion:r};case"UPDATE_CONSENSUS":if(!e.currentDiscussion)return e;const s={...e.currentDiscussion,consensusScore:t.payload.consensusScore,consensus:t.payload.consensus||e.currentDiscussion.consensus,status:t.payload.consensusScore>80?"consensus":e.currentDiscussion.status,moderatorInterventions:t.payload.moderatorInterventions??e.currentDiscussion.moderatorInterventions,topicRelevanceScore:t.payload.topicRelevanceScore??e.currentDiscussion.topicRelevanceScore,moderatorSummaries:t.payload.moderatorSummaries??e.currentDiscussion.moderatorSummaries};return{...e,currentDiscussion:s};case"END_DISCUSSION":if(!e.currentDiscussion)return e;const i={...e.currentDiscussion,status:"ended"};return{...e,currentDiscussion:null,allDiscussions:[i,...e.allDiscussions],isDiscussionActive:!1};case"LOAD_STATE":return t.payload;case"UPDATE_SETTINGS":return{...e,settings:t.payload};case"UPDATE_PREFERENCES":return{...e,preferences:t.payload};case"SET_LOADING":return{...e,isLoading:t.payload};case"SET_LOADING_STEP":return{...e,loadingStep:t.payload};case"SET_ALL_DISCUSSIONS":return{...e,allDiscussions:t.payload};case"INITIALIZE_SUCCESS":return{...e,agents:t.payload.agents,allDiscussions:t.payload.discussions,settings:t.payload.settings,preferences:t.payload.preferences,isLoading:!1,loadingStep:"complete"};default:return e}}function Tm({children:e}){const[t,n]=T.useReducer(Lm,Oc);T.useEffect(()=>{let p=!1;return(async()=>{if(p)return;const c=Date.now();try{console.log("Starting app initialization..."),n({type:"SET_LOADING",payload:!0}),n({type:"SET_LOADING_STEP",payload:"storage"}),console.log("Initializing storage service...");const d=Date.now();if(await R.initialize(),p||(console.log(`Storage service initialized in ${Date.now()-d}ms`),n({type:"SET_LOADING_STEP",payload:"server"}),R.isServerAvailable()||console.warn("后端服务器不可用，系统无法正常工作"),p))return;n({type:"SET_LOADING_STEP",payload:"agents"}),console.log("Loading data...");const h=Date.now(),N=await y();n({type:"SET_LOADING_STEP",payload:"discussions"}),console.log(`Data loaded in ${Date.now()-h}ms`),n({type:"INITIALIZE_SUCCESS",payload:N}),console.log(`App initialization completed in ${Date.now()-c}ms`)}catch(d){throw console.error("Failed to initialize app:",d),console.error("Error details:",{message:d instanceof Error?d.message:"Unknown error",stack:d instanceof Error?d.stack:void 0,initTime:Date.now()-c}),d}})(),()=>{p=!0}},[]),T.useEffect(()=>{if(!t.isLoading){const p=setTimeout(async()=>{try{console.log("Auto-saving data...");const g=[];g.push(R.saveAgents(t.agents));const c=t.currentDiscussion?[...t.allDiscussions,t.currentDiscussion]:t.allDiscussions;g.push(R.saveDiscussions(c)),t.settings&&g.push(R.saveSettings(t.settings)),t.preferences&&g.push(R.savePreferences(t.preferences)),await Promise.all(g),console.log("Auto-save completed")}catch(g){console.error("Failed to auto-save data:",g)}},1e3);return()=>clearTimeout(p)}},[t.agents,t.allDiscussions,t.currentDiscussion,t.settings,t.preferences,t.isLoading]);const r=p=>{const g={...p,id:ui(),isActive:!0};n({type:"ADD_AGENT",payload:g})},s=p=>{n({type:"UPDATE_AGENT",payload:p})},i=async p=>{try{await R.deleteAgent(p),n({type:"DELETE_AGENT",payload:p})}catch(g){throw console.error("Failed to delete agent:",g),g}},o=p=>{n({type:"START_DISCUSSION",payload:p})},a=async()=>{if(t.currentDiscussion)try{const p={...t.currentDiscussion,status:"ended"};await R.saveDiscussion(p),n({type:"END_DISCUSSION"});const g=await R.getDiscussions();n({type:"SET_ALL_DISCUSSIONS",payload:g})}catch(p){console.error("Failed to save discussion:",p),n({type:"END_DISCUSSION"})}else n({type:"END_DISCUSSION"})},u=async(p,g,c)=>{const d={id:ui(),agentId:g,content:p,type:c,timestamp:new Date};if(n({type:"ADD_MESSAGE",payload:d}),t.currentDiscussion)try{await O.addMessage(t.currentDiscussion.id,d)}catch(f){console.error("Failed to save message:",f)}},m=p=>{n({type:"UPDATE_SETTINGS",payload:p})},x=p=>{n({type:"UPDATE_PREFERENCES",payload:p})},y=async()=>{try{const[p,g,c,d]=await Promise.all([R.getAgents(),R.getDiscussions(),R.getSettings(),R.getPreferences()]);return{agents:p||[],discussions:g||[],settings:c||{},preferences:d||{}}}catch(p){throw console.error("Failed to load data:",p),p}},v=async()=>{try{const p=await R.getAllData();return JSON.stringify(p,null,2)}catch(p){throw console.error("Failed to export data:",p),new Error("导出数据失败")}},w=async p=>{try{const g=JSON.parse(p);if(!R.validateData(g))throw new Error("数据格式无效");await R.importAllData(g);const c=await y();return n({type:"INITIALIZE_SUCCESS",payload:c}),!0}catch(g){return console.error("Failed to import data:",g),!1}},j=async()=>{try{await R.clearAllData(),n({type:"INITIALIZE_SUCCESS",payload:{agents:[],discussions:[],settings:{},preferences:{}}})}catch(p){throw console.error("Failed to clear data:",p),new Error("清除数据失败")}};return l.jsx(Uc.Provider,{value:{state:t,dispatch:n,addAgent:r,updateAgent:s,deleteAgent:i,startDiscussion:o,endDiscussion:a,sendMessage:u,updateSettings:m,updatePreferences:x,exportData:v,importData:w,clearAllData:j},children:e})}const Lt=()=>{const e=T.useContext(Uc);if(!e)throw new Error("useApp must be used within AppProvider");return e};var Mm={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};const Dm=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),_m=(e,t)=>{const n=T.forwardRef(({color:r="currentColor",size:s=24,strokeWidth:i=2,absoluteStrokeWidth:o,children:a,...u},m)=>T.createElement("svg",{ref:m,...Mm,width:s,height:s,stroke:r,strokeWidth:o?Number(i)*24/Number(s):i,className:`lucide lucide-${Dm(e)}`,...u},[...t.map(([x,y])=>T.createElement(x,y)),...(Array.isArray(a)?a:[a])||[]]));return n.displayName=`${e}`,n};var _=_m;const Sn=_("AlertCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]]),ci=_("AlertTriangle",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3Z",key:"c3ski4"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]]),Am=_("BarChart3",[["path",{d:"M3 3v18h18",key:"1s2lah"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]]),jt=_("Brain",[["path",{d:"M9.5 2A2.5 2.5 0 0 1 12 4.5v15a2.5 2.5 0 0 1-4.96.44 2.5 2.5 0 0 1-2.96-3.08 3 3 0 0 1-.34-5.58 2.5 2.5 0 0 1 1.32-4.24 2.5 2.5 0 0 1 1.98-3A2.5 2.5 0 0 1 9.5 2Z",key:"1mhkh5"}],["path",{d:"M14.5 2A2.5 2.5 0 0 0 12 4.5v15a2.5 2.5 0 0 0 4.96.44 2.5 2.5 0 0 0 2.96-3.08 3 3 0 0 0 .34-5.58 2.5 2.5 0 0 0-1.32-4.24 2.5 2.5 0 0 0-1.98-3A2.5 2.5 0 0 0 14.5 2Z",key:"1d6s00"}]]),Pm=_("Calendar",[["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",ry:"2",key:"eu3xkr"}],["line",{x1:"16",x2:"16",y1:"2",y2:"6",key:"m3sa8f"}],["line",{x1:"8",x2:"8",y1:"2",y2:"6",key:"18kwsl"}],["line",{x1:"3",x2:"21",y1:"10",y2:"10",key:"xt86sb"}]]),kn=_("CheckCircle",[["path",{d:"M22 11.08V12a10 10 0 1 1-5.93-9.14",key:"g774vq"}],["polyline",{points:"22 4 12 14.01 9 11.01",key:"6xbx8j"}]]),Im=_("ChevronDown",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]]),zm=_("ChevronUp",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]]),Fc=_("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]]),Vc=_("Cpu",[["rect",{x:"4",y:"4",width:"16",height:"16",rx:"2",key:"1vbyd7"}],["rect",{x:"9",y:"9",width:"6",height:"6",key:"o3kz5p"}],["path",{d:"M15 2v2",key:"13l42r"}],["path",{d:"M15 20v2",key:"15mkzm"}],["path",{d:"M2 15h2",key:"1gxd5l"}],["path",{d:"M2 9h2",key:"1bbxkp"}],["path",{d:"M20 15h2",key:"19e6y8"}],["path",{d:"M20 9h2",key:"19tzq7"}],["path",{d:"M9 2v2",key:"165o2o"}],["path",{d:"M9 20v2",key:"i2bqo8"}]]),uo=_("Database",[["ellipse",{cx:"12",cy:"5",rx:"9",ry:"3",key:"msslwz"}],["path",{d:"M3 5V19A9 3 0 0 0 21 19V5",key:"1wlel7"}],["path",{d:"M3 12A9 3 0 0 0 21 12",key:"mv7ke4"}]]),di=_("Download",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]]),Rm=_("EyeOff",[["path",{d:"M9.88 9.88a3 3 0 1 0 4.24 4.24",key:"1jxqfv"}],["path",{d:"M10.73 5.08A10.43 10.43 0 0 1 12 5c7 0 10 7 10 7a13.16 13.16 0 0 1-1.67 2.68",key:"9wicm4"}],["path",{d:"M6.61 6.61A13.526 13.526 0 0 0 2 12s3 7 10 7a9.74 9.74 0 0 0 5.39-1.61",key:"1jreej"}],["line",{x1:"2",x2:"22",y1:"2",y2:"22",key:"a6p6uj"}]]),$m=_("Eye",[["path",{d:"M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z",key:"rwhkz3"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]),Om=_("FileText",[["path",{d:"M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z",key:"1nnpy2"}],["polyline",{points:"14 2 14 8 20 8",key:"1ew0cm"}],["line",{x1:"16",x2:"8",y1:"13",y2:"13",key:"14keom"}],["line",{x1:"16",x2:"8",y1:"17",y2:"17",key:"17nazh"}],["line",{x1:"10",x2:"8",y1:"9",y2:"9",key:"1a5vjj"}]]),Um=_("HardDrive",[["line",{x1:"22",x2:"2",y1:"12",y2:"12",key:"1y58io"}],["path",{d:"M5.45 5.11 2 12v6a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2v-6l-3.45-6.89A2 2 0 0 0 16.76 4H7.24a2 2 0 0 0-1.79 1.11z",key:"oot6mr"}],["line",{x1:"6",x2:"6.01",y1:"16",y2:"16",key:"sgf278"}],["line",{x1:"10",x2:"10.01",y1:"16",y2:"16",key:"1l4acy"}]]),Fm=_("HelpCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3",key:"1u773s"}],["path",{d:"M12 17h.01",key:"p32p05"}]]),Ns=_("History",[["path",{d:"M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8",key:"1357e3"}],["path",{d:"M3 3v5h5",key:"1xhq8a"}],["path",{d:"M12 7v5l4 2",key:"1fdv2h"}]]),Vm=_("Info",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 16v-4",key:"1dtifu"}],["path",{d:"M12 8h.01",key:"e9boi3"}]]),ja=_("Loader",[["line",{x1:"12",x2:"12",y1:"2",y2:"6",key:"gza1u7"}],["line",{x1:"12",x2:"12",y1:"18",y2:"22",key:"1qhbu9"}],["line",{x1:"4.93",x2:"7.76",y1:"4.93",y2:"7.76",key:"xae44r"}],["line",{x1:"16.24",x2:"19.07",y1:"16.24",y2:"19.07",key:"bxnmvf"}],["line",{x1:"2",x2:"6",y1:"12",y2:"12",key:"89khin"}],["line",{x1:"18",x2:"22",y1:"12",y2:"12",key:"pb8tfm"}],["line",{x1:"4.93",x2:"7.76",y1:"19.07",y2:"16.24",key:"1uxjnu"}],["line",{x1:"16.24",x2:"19.07",y1:"7.76",y2:"4.93",key:"6duxfx"}]]),Na=_("MessageCircle",[["path",{d:"m3 21 1.9-5.7a8.5 8.5 0 1 1 3.8 3.8z",key:"v2veuj"}]]),et=_("MessageSquare",[["path",{d:"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z",key:"1lielz"}]]),Hc=_("PenSquare",[["path",{d:"M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1qinfi"}],["path",{d:"M18.5 2.5a2.12 2.12 0 0 1 3 3L12 15l-4 1 1-4Z",key:"w2jsv5"}]]),Hm=_("Play",[["polygon",{points:"5 3 19 12 5 21 5 3",key:"191637"}]]),fi=_("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]]),Bm=_("RefreshCw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]]),Wm=_("Save",[["path",{d:"M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z",key:"1owoqh"}],["polyline",{points:"17 21 17 13 7 13 7 21",key:"1md35c"}],["polyline",{points:"7 3 7 8 15 8",key:"8nz8an"}]]),Qm=_("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]]),Nt=_("Settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]),Km=_("StopCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["rect",{width:"6",height:"6",x:"9",y:"9",key:"1wrtvo"}]]),Gm=_("TestTube",[["path",{d:"M14.5 2v17.5c0 1.4-1.1 2.5-2.5 2.5h0c-1.4 0-2.5-1.1-2.5-2.5V2",key:"187lwq"}],["path",{d:"M8.5 2h7",key:"csnxdl"}],["path",{d:"M14.5 16h-5",key:"1ox875"}]]),Ym=_("ThumbsDown",[["path",{d:"M17 14V2",key:"8ymqnk"}],["path",{d:"M9 18.12 10 14H4.17a2 2 0 0 1-1.92-2.56l2.33-8A2 2 0 0 1 6.5 2H20a2 2 0 0 1 2 2v8a2 2 0 0 1-2 2h-2.76a2 2 0 0 0-1.79 1.11L12 22h0a3.13 3.13 0 0 1-3-3.88Z",key:"s6e0r"}]]),Xm=_("ThumbsUp",[["path",{d:"M7 10v12",key:"1qc93n"}],["path",{d:"M15 5.88 14 10h5.83a2 2 0 0 1 1.92 2.56l-2.33 8A2 2 0 0 1 17.5 22H4a2 2 0 0 1-2-2v-8a2 2 0 0 1 2-2h2.76a2 2 0 0 0 1.79-1.11L12 2h0a3.13 3.13 0 0 1 3 3.88Z",key:"y3tblf"}]]),mr=_("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]]),Bc=_("TrendingUp",[["polyline",{points:"22 7 13.5 15.5 8.5 10.5 2 17",key:"126l90"}],["polyline",{points:"16 7 22 7 22 13",key:"kwv8wd"}]]),mi=_("Upload",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"17 8 12 3 7 8",key:"t8dd8p"}],["line",{x1:"12",x2:"12",y1:"3",y2:"15",key:"widbto"}]]),qm=_("User",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]]),Qt=_("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]]),Zm=_("Wrench",[["path",{d:"M14.7 6.3a1 1 0 0 0 0 1.4l1.6 1.6a1 1 0 0 0 1.4 0l3.77-3.77a6 6 0 0 1-7.94 7.94l-6.91 6.91a2.12 2.12 0 0 1-3-3l6.91-6.91a6 6 0 0 1 7.94-7.94l-3.76 3.76z",key:"cbrjhi"}]]),Jm=_("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]]),Sa=_("Zap",[["polygon",{points:"13 2 3 14 12 14 11 22 21 10 12 10 13 2",key:"45s27k"}]]),ep=[{id:"gpt-4-turbo",name:"GPT-4 Turbo",provider:"OpenAI",model:"gpt-4-turbo-preview",description:"OpenAI最新的GPT-4 Turbo模型，性能强大，适合复杂推理",defaultSettings:{temperature:.7,maxTokens:1e3}},{id:"gpt-4",name:"GPT-4",provider:"OpenAI",model:"gpt-4",description:"OpenAI的旗舰模型，适合需要高质量输出的场景",defaultSettings:{temperature:.7,maxTokens:1e3}},{id:"gpt-3.5-turbo",name:"GPT-3.5 Turbo",provider:"OpenAI",model:"gpt-3.5-turbo",description:"快速且经济的模型，适合大多数对话任务",defaultSettings:{temperature:.7,maxTokens:800}},{id:"claude-3-opus",name:"Claude 3 Opus",provider:"Anthropic",model:"claude-3-opus-20240229",description:"Anthropic最强大的模型，擅长复杂分析和创作",defaultSettings:{temperature:.7,maxTokens:1e3}},{id:"claude-3-sonnet",name:"Claude 3 Sonnet",provider:"Anthropic",model:"claude-3-sonnet-20240229",description:"平衡性能和成本的优秀选择",defaultSettings:{temperature:.7,maxTokens:800}},{id:"claude-3-haiku",name:"Claude 3 Haiku",provider:"Anthropic",model:"claude-3-haiku-20240307",description:"快速响应的轻量级模型，适合简单任务",defaultSettings:{temperature:.7,maxTokens:600}},{id:"gpt-4-azure",name:"Azure GPT-4",provider:"Azure",model:"gpt-4",description:"部署在Azure上的GPT-4模型",defaultSettings:{temperature:.7,maxTokens:1e3}},{id:"gpt-35-turbo-azure",name:"Azure GPT-3.5 Turbo",provider:"Azure",model:"gpt-35-turbo",description:"部署在Azure上的GPT-3.5 Turbo模型",defaultSettings:{temperature:.7,maxTokens:800}}],ka=ep;function Ss(e){return{openai:"🤖",anthropic:"🧠",azure:"☁️",custom:"⚙️"}[e.toLowerCase()]||"🔧"}function ks(e){return{openai:"bg-green-100 text-green-800",anthropic:"bg-blue-100 text-blue-800",azure:"bg-purple-100 text-purple-800",custom:"bg-gray-100 text-gray-800"}[e.toLowerCase()]||"bg-gray-100 text-gray-800"}const Ca=["/images/agent-alex.jpg","/images/agent-luna.jpg","/images/agent-max.jpg","/images/agent-chen.jpg","/images/agent-sam.jpg","/images/agent-robin.jpg","/images/agent-taylor.jpg","/images/agent-zoe.jpg"],tp=["技术","商业","设计","营销","数据分析","产品管理","法律","心理学","教育","医疗"],Wc=[{value:"logical",label:"逻辑型"},{value:"creative",label:"创意型"},{value:"analytical",label:"分析型"},{value:"intuitive",label:"直觉型"},{value:"systematic",label:"系统型"}],np=[{value:"assertive",label:"果断型"},{value:"collaborative",label:"协作型"},{value:"diplomatic",label:"外交型"},{value:"direct",label:"直接型"},{value:"thoughtful",label:"深思型"}],rp=["数据查询","市场分析","技术调研","用户调研","竞品分析","风险评估","法律咨询","创意生成"];function sp(){const{state:e,addAgent:t,updateAgent:n,deleteAgent:r}=Lt(),[s,i]=T.useState(!1),[o,a]=T.useState(null),u=async x=>{if(confirm("确定要删除这个智能体吗？"))try{await r(x)}catch(y){alert("删除智能体失败: "+(y instanceof Error?y.message:"未知错误"))}},m=x=>{o?(n({...x,id:o.id,isActive:o.isActive}),a(null)):(t(x),i(!1))};return l.jsx("div",{className:"h-full bg-gradient-to-br from-slate-50 to-blue-50 w-full overflow-y-auto",children:l.jsx("div",{className:"centered-container",children:l.jsxs("div",{className:"centered-content",children:[l.jsxs("div",{className:"flex items-center justify-between mb-8",children:[l.jsxs("div",{children:[l.jsx("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:"智能体管理"}),l.jsx("p",{className:"text-gray-600",children:"配置和管理您的AI智能体团队"})]}),l.jsxs("button",{onClick:()=>i(!0),className:"flex items-center gap-2 bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors shadow-lg",children:[l.jsx(fi,{size:20}),"添加智能体"]})]}),l.jsx("div",{className:"flex flex-wrap gap-6 mb-8",children:e.agents.map(x=>l.jsx(lp,{agent:x,onEdit:()=>a(x),onDelete:()=>u(x.id)},x.id))}),(s||o)&&l.jsx(ip,{agent:o,onSubmit:m,onCancel:()=>{i(!1),a(null)}})]})})})}function lp({agent:e,onEdit:t,onDelete:n}){var r,s,i;return l.jsxs("div",{className:"bg-white rounded-xl shadow-lg hover:shadow-xl transition-shadow p-6 border border-gray-100 fixed-size-card",children:[l.jsxs("div",{className:"flex items-center gap-4 mb-4",children:[l.jsx("img",{src:e.avatar,alt:e.name,className:"w-16 h-16 rounded-full object-cover border-4 border-blue-100"}),l.jsxs("div",{children:[l.jsx("h3",{className:"text-xl font-semibold text-gray-900",children:e.name}),l.jsx("span",{className:`inline-block px-2 py-1 rounded-full text-xs font-medium ${e.isActive?"bg-green-100 text-green-800":"bg-gray-100 text-gray-600"}`,children:e.isActive?"活跃":"非活跃"})]})]}),l.jsxs("div",{className:"space-y-3 mb-4",children:[l.jsxs("div",{className:"flex items-center gap-2",children:[l.jsx(qm,{size:16,className:"text-blue-600"}),l.jsx("span",{className:"text-sm text-gray-600",children:"专业领域："}),l.jsxs("div",{className:"flex flex-wrap gap-1",children:[e.expertise.slice(0,2).map((o,a)=>l.jsx("span",{className:"bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded",children:o},a)),e.expertise.length>2&&l.jsxs("span",{className:"text-xs text-gray-500",children:["+",e.expertise.length-2]})]})]}),l.jsxs("div",{className:"flex items-center gap-2",children:[l.jsx(jt,{size:16,className:"text-purple-600"}),l.jsx("span",{className:"text-sm text-gray-600",children:"思维方式："}),l.jsx("span",{className:"text-sm font-medium text-gray-900",children:(r=Wc.find(o=>o.value===e.thinkingStyle))==null?void 0:r.label})]}),l.jsxs("div",{className:"flex items-center gap-2",children:[l.jsx(Zm,{size:16,className:"text-green-600"}),l.jsx("span",{className:"text-sm text-gray-600",children:"工具："}),l.jsxs("span",{className:"text-sm text-gray-500",children:[e.tools.length," 个"]})]}),e.llmConfig&&l.jsxs("div",{className:"flex items-center gap-2",children:[l.jsx(Nt,{size:16,className:"text-orange-600"}),l.jsx("span",{className:"text-sm text-gray-600",children:"LLM："}),l.jsxs("div",{className:"flex items-center gap-1",children:[l.jsx("span",{className:"text-sm",children:Ss(e.llmConfig.provider)}),l.jsx("span",{className:`text-xs px-2 py-0.5 rounded ${ks(e.llmConfig.provider)}`,children:e.llmConfig.name})]})]}),e.isModerator&&l.jsxs("div",{className:"flex items-center gap-2",children:[l.jsx("div",{className:"w-4 h-4 bg-purple-600 rounded-full flex items-center justify-center",children:l.jsx("span",{className:"text-white text-xs font-bold",children:"主"})}),l.jsx("span",{className:"text-sm text-gray-600",children:"可做主持人"}),l.jsx("span",{className:"text-xs bg-purple-100 text-purple-800 px-2 py-0.5 rounded",children:((s=e.moderatorConfig)==null?void 0:s.managementStyle)==="strict"?"严格型":((i=e.moderatorConfig)==null?void 0:i.managementStyle)==="flexible"?"灵活型":"协作型"})]})]}),l.jsxs("div",{className:"flex gap-2",children:[l.jsxs("button",{onClick:t,className:"flex-1 bg-blue-50 text-blue-600 py-2 rounded-lg hover:bg-blue-100 transition-colors flex items-center justify-center gap-1",children:[l.jsx(Hc,{size:16}),"编辑"]}),l.jsxs("button",{onClick:n,className:"flex-1 bg-red-50 text-red-600 py-2 rounded-lg hover:bg-red-100 transition-colors flex items-center justify-center gap-1",children:[l.jsx(mr,{size:16}),"删除"]})]})]})}function ip({agent:e,onSubmit:t,onCancel:n}){var v;const[r,s]=T.useState({name:(e==null?void 0:e.name)||"",avatar:(e==null?void 0:e.avatar)||Ca[0],expertise:(e==null?void 0:e.expertise)||[],thinkingStyle:(e==null?void 0:e.thinkingStyle)||"logical",personality:(e==null?void 0:e.personality)||"collaborative",tools:(e==null?void 0:e.tools)||[],llmConfig:(e==null?void 0:e.llmConfig)||null,isModerator:(e==null?void 0:e.isModerator)||!1,moderatorConfig:(e==null?void 0:e.moderatorConfig)||{summaryFrequency:5,interventionThreshold:.6,managementStyle:"flexible",autoTerminate:!0,maxInterventions:5}}),[i,o]=T.useState([]),[a,u]=T.useState(!0);T.useEffect(()=>{(async()=>{try{u(!0);const j=await R.getLLMConfigs();o(j)}catch(j){console.error("Failed to load LLM configs:",j),o([])}finally{u(!1)}})()},[]);const m=w=>{w.preventDefault(),r.name&&r.expertise.length>0&&r.llmConfig&&t({...r,llmConfig:r.llmConfig,isModerator:r.isModerator,moderatorConfig:r.isModerator?r.moderatorConfig:void 0})},x=w=>{s(j=>({...j,expertise:j.expertise.includes(w)?j.expertise.filter(p=>p!==w):[...j.expertise,w]}))},y=w=>{s(j=>({...j,tools:j.tools.includes(w)?j.tools.filter(p=>p!==w):[...j.tools,w]}))};return l.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50",children:l.jsxs("div",{className:"bg-white rounded-xl shadow-2xl max-w-2xl w-full max-h-[90vh] overflow-y-auto",children:[l.jsx("div",{className:"p-6 border-b border-gray-200",children:l.jsx("h2",{className:"text-2xl font-bold text-gray-900",children:e?"编辑智能体":"添加新智能体"})}),l.jsxs("form",{onSubmit:m,className:"p-6 space-y-6",children:[l.jsxs("div",{children:[l.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"智能体名称"}),l.jsx("input",{type:"text",value:r.name,onChange:w=>s(j=>({...j,name:w.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"输入智能体名称",required:!0})]}),l.jsxs("div",{children:[l.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"选择头像"}),l.jsx("div",{className:"grid grid-cols-4 gap-3",children:Ca.map((w,j)=>l.jsx("button",{type:"button",onClick:()=>s(p=>({...p,avatar:w})),className:`relative rounded-lg overflow-hidden border-4 transition-all ${r.avatar===w?"border-blue-500 ring-2 ring-blue-200":"border-gray-200 hover:border-gray-300"}`,children:l.jsx("img",{src:w,alt:`Avatar ${j+1}`,className:"w-16 h-16 object-cover"})},j))})]}),l.jsxs("div",{children:[l.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"专业领域（至少选择一个）"}),l.jsx("div",{className:"grid grid-cols-2 gap-2",children:tp.map(w=>l.jsx("button",{type:"button",onClick:()=>x(w),className:`px-3 py-2 rounded-lg text-sm transition-colors ${r.expertise.includes(w)?"bg-blue-100 text-blue-800 border-2 border-blue-300":"bg-gray-100 text-gray-700 border-2 border-gray-300 hover:bg-gray-200"}`,children:w},w))})]}),l.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[l.jsxs("div",{children:[l.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"思维方式"}),l.jsx("select",{value:r.thinkingStyle,onChange:w=>s(j=>({...j,thinkingStyle:w.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:Wc.map(w=>l.jsx("option",{value:w.value,children:w.label},w.value))})]}),l.jsxs("div",{children:[l.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"性格特征"}),l.jsx("select",{value:r.personality,onChange:w=>s(j=>({...j,personality:w.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:np.map(w=>l.jsx("option",{value:w.value,children:w.label},w.value))})]})]}),l.jsxs("div",{children:[l.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"可用工具"}),l.jsx("div",{className:"grid grid-cols-2 gap-2",children:rp.map(w=>l.jsx("button",{type:"button",onClick:()=>y(w),className:`px-3 py-2 rounded-lg text-sm transition-colors ${r.tools.includes(w)?"bg-green-100 text-green-800 border-2 border-green-300":"bg-gray-100 text-gray-700 border-2 border-gray-300 hover:bg-gray-200"}`,children:w},w))})]}),l.jsxs("div",{children:[l.jsxs("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:["LLM配置 ",l.jsx("span",{className:"text-red-500",children:"*"})]}),a?l.jsxs("div",{className:"w-full px-3 py-2 border border-gray-300 rounded-lg bg-gray-50 flex items-center gap-2",children:[l.jsx("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"}),l.jsx("span",{className:"text-gray-600",children:"正在加载LLM配置..."})]}):l.jsxs("select",{value:((v=r.llmConfig)==null?void 0:v.id)||"",onChange:w=>{const j=i.find(p=>p.id===w.target.value);s(p=>({...p,llmConfig:j||null}))},className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",required:!0,children:[l.jsx("option",{value:"",children:"请选择LLM配置"}),i.map(w=>l.jsxs("option",{value:w.id,children:[Ss(w.provider)," ",w.name," (",w.provider.toUpperCase(),")"]},w.id))]}),r.llmConfig&&l.jsxs("div",{className:"mt-2 p-3 bg-gray-50 rounded-lg",children:[l.jsxs("div",{className:"flex items-center gap-2 mb-1",children:[l.jsx("span",{className:"text-sm font-medium text-gray-700",children:"已选择:"}),l.jsx("span",{className:`text-xs px-2 py-1 rounded ${ks(r.llmConfig.provider)}`,children:r.llmConfig.name})]}),l.jsxs("div",{className:"text-xs text-gray-600",children:["模型: ",r.llmConfig.model," | 温度: ",r.llmConfig.temperature," | 令牌: ",r.llmConfig.maxTokens]})]}),i.length===0&&l.jsx("div",{className:"mt-2 p-3 bg-yellow-50 border border-yellow-200 rounded-lg",children:l.jsxs("p",{className:"text-sm text-yellow-800",children:["暂无可用的LLM配置。请先前往",l.jsx("span",{className:"font-medium",children:"LLM配置"}),"页面创建LLM配置，然后再创建智能体。"]})})]}),l.jsxs("div",{className:"border-t border-gray-200 pt-6",children:[l.jsxs("div",{className:"flex items-center gap-3 mb-4",children:[l.jsx("input",{type:"checkbox",id:"isModerator",checked:r.isModerator,onChange:w=>s(j=>({...j,isModerator:w.target.checked})),className:"w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 focus:ring-2"}),l.jsx("label",{htmlFor:"isModerator",className:"text-sm font-medium text-gray-700",children:"可以做主持人"})]}),r.isModerator&&l.jsxs("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4 space-y-4",children:[l.jsx("h4",{className:"font-medium text-blue-900 mb-3",children:"主持人配置"}),l.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[l.jsxs("div",{children:[l.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"总结频率（每N条消息）"}),l.jsx("input",{type:"number",min:"1",max:"20",value:r.moderatorConfig.summaryFrequency,onChange:w=>s(j=>({...j,moderatorConfig:{...j.moderatorConfig,summaryFrequency:parseInt(w.target.value)||5}})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]}),l.jsxs("div",{children:[l.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"干预阈值（0-1）"}),l.jsx("input",{type:"number",min:"0",max:"1",step:"0.1",value:r.moderatorConfig.interventionThreshold,onChange:w=>s(j=>({...j,moderatorConfig:{...j.moderatorConfig,interventionThreshold:parseFloat(w.target.value)||.6}})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]}),l.jsxs("div",{children:[l.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"管理风格"}),l.jsxs("select",{value:r.moderatorConfig.managementStyle,onChange:w=>s(j=>({...j,moderatorConfig:{...j.moderatorConfig,managementStyle:w.target.value}})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:[l.jsx("option",{value:"strict",children:"严格型"}),l.jsx("option",{value:"flexible",children:"灵活型"}),l.jsx("option",{value:"collaborative",children:"协作型"})]})]}),l.jsxs("div",{children:[l.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"最大干预次数"}),l.jsx("input",{type:"number",min:"1",max:"20",value:r.moderatorConfig.maxInterventions,onChange:w=>s(j=>({...j,moderatorConfig:{...j.moderatorConfig,maxInterventions:parseInt(w.target.value)||5}})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]})]}),l.jsxs("div",{className:"flex items-center gap-3",children:[l.jsx("input",{type:"checkbox",id:"autoTerminate",checked:r.moderatorConfig.autoTerminate,onChange:w=>s(j=>({...j,moderatorConfig:{...j.moderatorConfig,autoTerminate:w.target.checked}})),className:"w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 focus:ring-2"}),l.jsx("label",{htmlFor:"autoTerminate",className:"text-sm font-medium text-gray-700",children:"自动终止讨论"})]})]})]}),l.jsxs("div",{className:"flex gap-3 pt-4 border-t border-gray-200",children:[l.jsx("button",{type:"button",onClick:n,className:"flex-1 px-4 py-3 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors",children:"取消"}),l.jsx("button",{type:"submit",disabled:a||i.length===0,className:"flex-1 px-4 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors",children:a?"加载中...":`${e?"更新":"创建"}智能体`})]})]})]})})}function op(){var x,y,v,w,j;const{state:e,startDiscussion:t}=Lt(),[n,r]=T.useState({topic:"",mode:"free",selectedAgents:[],moderatorId:void 0}),[s,i]=T.useState([]),o=()=>{const p=[];return n.topic.trim()||p.push("请输入讨论话题"),n.selectedAgents.length<2&&p.push("至少需要选择2个智能体参与讨论"),n.selectedAgents.length>8&&p.push("最多支持8个智能体同时讨论"),n.mode==="moderator"&&!n.moderatorId&&(n.selectedAgents.map(c=>m.find(d=>d.id===c)).filter(c=>c&&c.isModerator).length===0?p.push("主持人模式需要至少一个具备主持人能力的智能体"):p.push("主持人模式下请选择一个主持人")),i(p),p.length===0},a=()=>{o()&&t(n)},u=p=>{r(g=>({...g,selectedAgents:g.selectedAgents.includes(p)?g.selectedAgents.filter(c=>c!==p):[...g.selectedAgents,p]}))},m=e.agents.filter(p=>p.isActive);return m.length===0?l.jsx("div",{className:"h-full bg-gradient-to-br from-orange-50 to-red-50 overflow-y-auto",children:l.jsx("div",{className:"max-w-4xl mx-auto p-6",children:l.jsxs("div",{className:"bg-white rounded-xl shadow-lg p-8 text-center",children:[l.jsx(Sn,{size:64,className:"text-orange-500 mx-auto mb-4"}),l.jsx("h2",{className:"text-2xl font-bold text-gray-900 mb-4",children:"没有可用的智能体"}),l.jsx("p",{className:"text-gray-600 mb-6",children:"您需要先创建和配置智能体才能开始讨论。"}),l.jsx("button",{onClick:()=>window.location.reload(),className:"bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors",children:"前往智能体管理"})]})})}):l.jsx("div",{className:"h-full bg-gradient-to-br from-purple-50 to-pink-50 w-full overflow-y-auto",children:l.jsx("div",{className:"flex justify-center p-6",children:l.jsxs("div",{className:"bg-white rounded-xl shadow-lg overflow-hidden",style:{width:"800px",maxWidth:"800px"},children:[l.jsxs("div",{className:"bg-gradient-to-r from-purple-600 to-pink-600 text-white p-8",children:[l.jsxs("div",{className:"flex items-center gap-3 mb-4",children:[l.jsx(Na,{size:32}),l.jsx("h1",{className:"text-3xl font-bold",children:"创建新讨论"})]}),l.jsx("p",{className:"text-purple-100",children:"配置讨论话题、模式和参与者，开始智能体之间的协作讨论"})]}),l.jsxs("div",{className:"p-8 space-y-8",children:[s.length>0&&l.jsxs("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4",children:[l.jsxs("div",{className:"flex items-center gap-2 mb-2",children:[l.jsx(Sn,{size:20,className:"text-red-600"}),l.jsx("h3",{className:"font-medium text-red-800",children:"配置错误"})]}),l.jsx("ul",{className:"text-red-700 text-sm space-y-1",children:s.map((p,g)=>l.jsxs("li",{children:["• ",p]},g))})]}),l.jsxs("div",{className:"space-y-3",children:[l.jsx("label",{className:"block text-lg font-semibold text-gray-900",children:"讨论话题"}),l.jsx("textarea",{value:n.topic,onChange:p=>r(g=>({...g,topic:p.target.value})),placeholder:"请输入您想要讨论的话题，例如：如何提升用户体验设计质量？",className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent resize-none",rows:3}),l.jsx("p",{className:"text-sm text-gray-500",children:"清晰的话题描述有助于智能体更好地理解和参与讨论"})]}),l.jsxs("div",{className:"space-y-4",children:[l.jsx("label",{className:"block text-lg font-semibold text-gray-900",children:"讨论模式"}),l.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[l.jsxs("button",{onClick:()=>r(p=>({...p,mode:"free"})),className:`p-6 rounded-xl border-2 transition-all text-left ${n.mode==="free"?"border-purple-500 bg-purple-50":"border-gray-200 hover:border-gray-300"}`,children:[l.jsxs("div",{className:"flex items-center gap-3 mb-3",children:[l.jsx(Na,{size:24,className:n.mode==="free"?"text-purple-600":"text-gray-600"}),l.jsx("h3",{className:"font-semibold text-gray-900",children:"自由讨论模式"})]}),l.jsx("p",{className:"text-gray-600 text-sm",children:"智能体根据话题相关性和兴趣自主发言，讨论更加自然流畅"})]}),l.jsxs("button",{onClick:()=>r(p=>({...p,mode:"moderator"})),className:`p-6 rounded-xl border-2 transition-all text-left ${n.mode==="moderator"?"border-purple-500 bg-purple-50":"border-gray-200 hover:border-gray-300"}`,children:[l.jsxs("div",{className:"flex items-center gap-3 mb-3",children:[l.jsx(Nt,{size:24,className:n.mode==="moderator"?"text-purple-600":"text-gray-600"}),l.jsx("h3",{className:"font-semibold text-gray-900",children:"主持人模式"})]}),l.jsx("p",{className:"text-gray-600 text-sm",children:"选择一个智能体作为主持人，按轮次组织讨论，更加有序规范"})]})]})]}),l.jsxs("div",{className:"space-y-4",children:[l.jsxs("div",{className:"flex items-center gap-3",children:[l.jsx(Qt,{size:24,className:"text-purple-600"}),l.jsxs("h2",{className:"text-lg font-semibold text-gray-900",children:["选择参与者 (",n.selectedAgents.length,"/8)"]})]}),l.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:m.map(p=>l.jsxs("button",{onClick:()=>u(p.id),className:`p-4 rounded-xl border-2 transition-all text-left ${n.selectedAgents.includes(p.id)?"border-purple-500 bg-purple-50":"border-gray-200 hover:border-gray-300"}`,children:[l.jsxs("div",{className:"flex items-center gap-3 mb-2",children:[l.jsx("img",{src:p.avatar,alt:p.name,className:"w-10 h-10 rounded-full object-cover"}),l.jsxs("div",{children:[l.jsx("h3",{className:"font-medium text-gray-900",children:p.name}),l.jsx("p",{className:"text-sm text-gray-500",children:p.expertise.slice(0,2).join("、")})]})]}),l.jsxs("div",{className:"flex items-center justify-between",children:[l.jsxs("div",{className:"flex items-center gap-2",children:[l.jsxs("span",{className:"text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded",children:[p.thinkingStyle==="logical"&&"逻辑型",p.thinkingStyle==="creative"&&"创意型",p.thinkingStyle==="analytical"&&"分析型",p.thinkingStyle==="intuitive"&&"直觉型",p.thinkingStyle==="systematic"&&"系统型"]}),p.isModerator&&l.jsx("span",{className:"text-xs bg-blue-100 text-blue-600 px-2 py-1 rounded",children:"主持人"})]}),n.selectedAgents.includes(p.id)&&l.jsx("div",{className:"w-5 h-5 bg-purple-500 rounded-full flex items-center justify-center",children:l.jsx("div",{className:"w-2 h-2 bg-white rounded-full"})})]})]},p.id))}),l.jsx("p",{className:"text-sm text-gray-500",children:"建议选择具有不同专业背景和思维方式的智能体，以获得更丰富的讨论视角"})]}),n.selectedAgents.length>0&&l.jsxs("div",{className:"space-y-4",children:[l.jsxs("div",{className:"flex items-center gap-3",children:[l.jsx(Nt,{size:24,className:"text-purple-600"}),l.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"主持人设置"})]}),l.jsxs("div",{className:"space-y-3",children:[l.jsx("label",{className:"block text-sm font-medium text-gray-700",children:"选择主持人（可选）"}),l.jsxs("select",{value:n.moderatorId||"",onChange:p=>r(g=>({...g,moderatorId:p.target.value||void 0})),className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent",children:[l.jsx("option",{value:"",children:"无主持人（系统自动管理）"}),n.selectedAgents.map(p=>m.find(g=>g.id===p)).filter(p=>p&&p.isModerator).map(p=>l.jsxs("option",{value:p.id,children:[p.name," - ",p.expertise.slice(0,2).join("、")]},p.id))]}),n.moderatorId&&l.jsxs("div",{className:"space-y-4",children:[(()=>{var g,c,d;const p=m.find(f=>f.id===n.moderatorId);return p?l.jsx("div",{className:"bg-gradient-to-r from-blue-50 to-purple-50 border border-blue-200 rounded-lg p-4",children:l.jsxs("div",{className:"flex items-start gap-4",children:[l.jsx("img",{src:p.avatar,alt:p.name,className:"w-12 h-12 rounded-full object-cover"}),l.jsxs("div",{className:"flex-1",children:[l.jsxs("div",{className:"flex items-center gap-2 mb-2",children:[l.jsx("h4",{className:"font-semibold text-gray-900",children:p.name}),l.jsx("span",{className:"text-xs bg-blue-100 text-blue-600 px-2 py-1 rounded",children:"主持人"})]}),l.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-3 text-sm",children:[l.jsxs("div",{children:[l.jsx("span",{className:"text-gray-600",children:"专业领域："}),l.jsx("span",{className:"text-gray-900",children:p.expertise.slice(0,3).join("、")})]}),l.jsxs("div",{children:[l.jsx("span",{className:"text-gray-600",children:"思维方式："}),l.jsxs("span",{className:"text-gray-900",children:[p.thinkingStyle==="logical"&&"逻辑型",p.thinkingStyle==="creative"&&"创意型",p.thinkingStyle==="analytical"&&"分析型",p.thinkingStyle==="intuitive"&&"直觉型",p.thinkingStyle==="systematic"&&"系统型"]})]}),l.jsxs("div",{children:[l.jsx("span",{className:"text-gray-600",children:"性格特征："}),l.jsxs("span",{className:"text-gray-900",children:[p.personality==="assertive"&&"果断型",p.personality==="collaborative"&&"协作型",p.personality==="diplomatic"&&"外交型",p.personality==="direct"&&"直接型",p.personality==="thoughtful"&&"深思型"]})]}),l.jsxs("div",{children:[l.jsx("span",{className:"text-gray-600",children:"管理风格："}),l.jsxs("span",{className:"text-gray-900",children:[((g=n.moderatorConfig)==null?void 0:g.managementStyle)==="strict"&&"严格型",((c=n.moderatorConfig)==null?void 0:c.managementStyle)==="flexible"&&"灵活型",((d=n.moderatorConfig)==null?void 0:d.managementStyle)==="collaborative"&&"协作型"]})]})]})]})]})}):null})(),l.jsx("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:l.jsxs("div",{className:"flex items-start gap-3",children:[l.jsx("div",{className:"w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0",children:l.jsx(Nt,{size:16,className:"text-blue-600"})}),l.jsxs("div",{className:"flex-1",children:[l.jsx("h4",{className:"font-medium text-blue-900 mb-1",children:"主持人职责"}),l.jsxs("ul",{className:"text-sm text-blue-700 space-y-1",children:[l.jsx("li",{children:"• 实时总结讨论内容"}),l.jsx("li",{children:"• 监控话题相关性"}),l.jsx("li",{children:"• 指定发言顺序（主持人模式）"}),l.jsx("li",{children:"• 引导讨论方向"})]})]})]})}),l.jsxs("div",{className:"bg-gray-50 border border-gray-200 rounded-lg p-4",children:[l.jsx("h4",{className:"font-medium text-gray-900 mb-3",children:"主持人配置"}),l.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[l.jsxs("div",{children:[l.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"总结频率"}),l.jsxs("select",{value:((x=n.moderatorConfig)==null?void 0:x.summaryFrequency)||5,onChange:p=>r(g=>{var c,d,f,h;return{...g,moderatorConfig:{...g.moderatorConfig,summaryFrequency:parseInt(p.target.value),interventionThreshold:((c=g.moderatorConfig)==null?void 0:c.interventionThreshold)||.6,managementStyle:((d=g.moderatorConfig)==null?void 0:d.managementStyle)||"flexible",autoTerminate:((f=g.moderatorConfig)==null?void 0:f.autoTerminate)||!0,maxInterventions:((h=g.moderatorConfig)==null?void 0:h.maxInterventions)||5}}}),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-purple-500 focus:border-transparent",children:[l.jsx("option",{value:3,children:"每3条消息"}),l.jsx("option",{value:5,children:"每5条消息"}),l.jsx("option",{value:8,children:"每8条消息"}),l.jsx("option",{value:10,children:"每10条消息"})]})]}),l.jsxs("div",{children:[l.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"管理风格"}),l.jsxs("select",{value:((y=n.moderatorConfig)==null?void 0:y.managementStyle)||"flexible",onChange:p=>r(g=>{var c,d,f,h;return{...g,moderatorConfig:{...g.moderatorConfig,summaryFrequency:((c=g.moderatorConfig)==null?void 0:c.summaryFrequency)||5,interventionThreshold:((d=g.moderatorConfig)==null?void 0:d.interventionThreshold)||.6,managementStyle:p.target.value,autoTerminate:((f=g.moderatorConfig)==null?void 0:f.autoTerminate)||!0,maxInterventions:((h=g.moderatorConfig)==null?void 0:h.maxInterventions)||5}}}),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-purple-500 focus:border-transparent",children:[l.jsx("option",{value:"strict",children:"严格型 - 严格控制发言顺序"}),l.jsx("option",{value:"flexible",children:"灵活型 - 适度引导讨论"}),l.jsx("option",{value:"collaborative",children:"协作型 - 鼓励自由交流"})]})]}),l.jsxs("div",{children:[l.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"干预阈值"}),l.jsxs("select",{value:((v=n.moderatorConfig)==null?void 0:v.interventionThreshold)||.6,onChange:p=>r(g=>{var c,d,f,h;return{...g,moderatorConfig:{...g.moderatorConfig,summaryFrequency:((c=g.moderatorConfig)==null?void 0:c.summaryFrequency)||5,interventionThreshold:parseFloat(p.target.value),managementStyle:((d=g.moderatorConfig)==null?void 0:d.managementStyle)||"flexible",autoTerminate:((f=g.moderatorConfig)==null?void 0:f.autoTerminate)||!0,maxInterventions:((h=g.moderatorConfig)==null?void 0:h.maxInterventions)||5}}}),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-purple-500 focus:border-transparent",children:[l.jsx("option",{value:.8,children:"高敏感度 - 轻微偏题即干预"}),l.jsx("option",{value:.6,children:"中等敏感度 - 适度偏题时干预"}),l.jsx("option",{value:.4,children:"低敏感度 - 严重偏题才干预"})]})]}),l.jsxs("div",{children:[l.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"最大干预次数"}),l.jsxs("select",{value:((w=n.moderatorConfig)==null?void 0:w.maxInterventions)||5,onChange:p=>r(g=>{var c,d,f,h;return{...g,moderatorConfig:{...g.moderatorConfig,summaryFrequency:((c=g.moderatorConfig)==null?void 0:c.summaryFrequency)||5,interventionThreshold:((d=g.moderatorConfig)==null?void 0:d.interventionThreshold)||.6,managementStyle:((f=g.moderatorConfig)==null?void 0:f.managementStyle)||"flexible",autoTerminate:((h=g.moderatorConfig)==null?void 0:h.autoTerminate)||!0,maxInterventions:parseInt(p.target.value)}}}),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-purple-500 focus:border-transparent",children:[l.jsx("option",{value:3,children:"3次"}),l.jsx("option",{value:5,children:"5次"}),l.jsx("option",{value:8,children:"8次"}),l.jsx("option",{value:-1,children:"不限制"})]})]})]}),l.jsx("div",{className:"mt-4",children:l.jsxs("label",{className:"flex items-center gap-2",children:[l.jsx("input",{type:"checkbox",checked:((j=n.moderatorConfig)==null?void 0:j.autoTerminate)||!0,onChange:p=>r(g=>{var c,d,f,h;return{...g,moderatorConfig:{...g.moderatorConfig,summaryFrequency:((c=g.moderatorConfig)==null?void 0:c.summaryFrequency)||5,interventionThreshold:((d=g.moderatorConfig)==null?void 0:d.interventionThreshold)||.6,managementStyle:((f=g.moderatorConfig)==null?void 0:f.managementStyle)||"flexible",autoTerminate:p.target.checked,maxInterventions:((h=g.moderatorConfig)==null?void 0:h.maxInterventions)||5}}}),className:"w-4 h-4 text-purple-600 border-gray-300 rounded focus:ring-purple-500"}),l.jsx("span",{className:"text-sm text-gray-700",children:"允许主持人自动终止讨论"})]})})]})]}),l.jsx("p",{className:"text-sm text-gray-500",children:"主持人将负责管理讨论流程，确保讨论高效有序进行"})]})]}),l.jsxs("div",{className:"border-t border-gray-200 pt-6",children:[l.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"高级设置（可选）"}),l.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[l.jsxs("div",{children:[l.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"最大消息数量"}),l.jsx("input",{type:"number",min:"10",max:"100",value:n.maxMessages||"",onChange:p=>r(g=>({...g,maxMessages:p.target.value?parseInt(p.target.value):void 0})),placeholder:"不限制",className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"})]}),l.jsxs("div",{children:[l.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"时间限制（分钟）"}),l.jsx("input",{type:"number",min:"5",max:"120",value:n.timeLimit||"",onChange:p=>r(g=>({...g,timeLimit:p.target.value?parseInt(p.target.value):void 0})),placeholder:"不限制",className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"})]})]})]}),l.jsx("div",{className:"flex justify-center pt-6",children:l.jsxs("button",{onClick:a,disabled:!n.topic.trim()||n.selectedAgents.length<2,className:"flex items-center gap-3 bg-gradient-to-r from-purple-600 to-pink-600 text-white px-8 py-4 rounded-xl hover:from-purple-700 hover:to-pink-700 disabled:from-gray-400 disabled:to-gray-500 disabled:cursor-not-allowed transition-all shadow-lg text-lg font-medium",children:[l.jsx(Hm,{size:24}),"开始讨论"]})})]})]})})})}const It=class It{static getInstance(){return It.instance||(It.instance=new It),It.instance}async callLLM(t,n){try{return await this.makeAPICall(t,n)}catch(r){throw console.error("LLM API调用失败:",r),new Error(`LLM调用失败: ${r instanceof Error?r.message:"未知错误"}`)}}async generateResponse(t,n){const r={messages:[{role:"user",content:n}],temperature:t.temperature||.7,maxTokens:t.maxTokens||500,model:t.model};return(await this.callLLM(t,r)).content}async generateAgentMessage(t,n,r,s=[]){const i=this.buildSystemPrompt(t,r),o=this.buildConversationHistory(s,t.id),a={messages:[{role:"system",content:i},...o,{role:"user",content:`请基于当前讨论情况，就"${r}"这个话题发表你的观点。`}],temperature:t.llmConfig.temperature||.7,maxTokens:t.llmConfig.maxTokens||500,model:t.llmConfig.model};return(await this.callLLM(t.llmConfig,a)).content}buildSystemPrompt(t,n){return`你是一个名为"${t.name}"的智能体，正在参与关于"${n}"的讨论。

你的特征：
- 专业领域：${t.expertise.join("、")}
- 思维方式：${t.thinkingStyle}
- 性格特征：${t.personality}
- 可用工具：${t.tools.join("、")}

请根据你的专业背景和性格特征参与讨论，保持角色一致性。你的回复应该：
1. 体现你的专业知识和思维方式
2. 符合你的性格特征
3. 简洁明了，通常在100-200字之间
4. 针对讨论话题提供有价值的观点

${t.llmConfig.systemPrompt||""}`}buildConversationHistory(t,n){return t.slice(-10).map(s=>({role:s.agentId===n?"assistant":"user",content:`${s.agentId===n?"我":"其他参与者"}：${s.content}`}))}async makeAPICall(t,n){const r=this.getAPIUrl(t),s=this.getAPIHeaders(t),i=this.formatRequestBody(t,n),o=await fetch(r,{method:"POST",headers:s,body:JSON.stringify(i)});if(!o.ok)throw new Error(`API请求失败: ${o.status} ${o.statusText}`);const a=await o.json();return this.parseResponse(t,a)}getAPIUrl(t){if(t.baseURL)return`${t.baseURL}/chat/completions`;switch(t.provider){case"openai":return"https://api.openai.com/v1/chat/completions";case"anthropic":return"https://api.anthropic.com/v1/messages";case"azure":return`${t.baseURL}/openai/deployments/${t.model}/chat/completions?api-version=2023-12-01-preview`;default:throw new Error(`不支持的提供商: ${t.provider}`)}}getAPIHeaders(t){const n={"Content-Type":"application/json"};switch(t.provider){case"openai":case"azure":case"custom":n.Authorization=`Bearer ${t.apiKey}`;break;case"anthropic":n["x-api-key"]=t.apiKey,n["anthropic-version"]="2023-06-01";break}return n}formatRequestBody(t,n){var r;switch(t.provider){case"anthropic":return{model:n.model||t.model,max_tokens:n.maxTokens||1e3,temperature:n.temperature||.7,messages:n.messages.filter(s=>s.role!=="system"),system:((r=n.messages.find(s=>s.role==="system"))==null?void 0:r.content)||""};default:return{model:n.model||t.model,messages:n.messages,temperature:n.temperature||.7,max_tokens:n.maxTokens||1e3}}}parseResponse(t,n){var r,s,i;switch(t.provider){case"anthropic":return{content:((r=n.content[0])==null?void 0:r.text)||"",usage:n.usage?{promptTokens:n.usage.input_tokens,completionTokens:n.usage.output_tokens,totalTokens:n.usage.input_tokens+n.usage.output_tokens}:void 0,model:n.model};default:return{content:((i=(s=n.choices[0])==null?void 0:s.message)==null?void 0:i.content)||"",usage:n.usage?{promptTokens:n.usage.prompt_tokens,completionTokens:n.usage.completion_tokens,totalTokens:n.usage.total_tokens}:void 0,model:n.model}}}async testLLMConfig(t){try{const n={messages:[{role:"user",content:'请回复"测试成功"'}],temperature:.1,maxTokens:10};return await this.callLLM(t,n),!0}catch(n){return console.error("LLM配置测试失败:",n),!1}}};Le(It,"instance");let pi=It;const Yt=pi.getInstance();async function ap(e,t,n,r=[]){if(!e.llmConfig)throw new Error(`智能体 ${e.name} 未配置LLM，无法生成消息`);try{return await Yt.generateAgentMessage(e,t,n,r)}catch(s){throw console.error(`智能体 ${e.name} 的LLM调用失败:`,s),new Error(`智能体 ${e.name} 的LLM调用失败: ${s instanceof Error?s.message:"未知错误"}`)}}function up(e,t){if(e.length<3)return 0;const n=e.slice(-10),r=n.filter(y=>y.type==="agreement").length/n.length,s=n.filter(y=>y.type==="disagreement").length/n.length,i=n.filter(y=>y.type==="question").length/n.length,o=new Map;n.forEach(y=>{o.set(y.agentId,(o.get(y.agentId)||0)+1)});const a=o.size,u=t.filter(y=>y.isActive).length,m=a/u;let x=0;return x+=r*40,x+=(1-s)*30,x+=i>.1&&i<.3?15:0,x+=m*15,Math.min(100,Math.max(0,x))}function cp(e,t){const r=e.slice(-15).filter(i=>i.type==="statement"||i.type==="agreement");if(r.length===0)return`关于"${t}"，参与者需要更多时间来达成共识。`;const s=dp(r.map(i=>i.content));return`经过充分讨论，大家就"${t}"达成了共识：${s.slice(0,3).join("、")}是关键要素，需要重点关注和实施。`}function dp(e){return["技术创新","用户体验","市场需求","成本控制","时间规划","质量保证","团队合作","数据分析"].filter(()=>Math.random()>.6).slice(0,5)}function qr(e,t,n){if(e.length===0)return null;if(n==="moderator"){const r=t.slice(-e.length).map(i=>i.agentId),s=e.filter(i=>!r.includes(i));return s.length>0?s[0]:e[0]}else{const r=t.slice(-10),s=new Map;e.forEach(u=>s.set(u,0)),r.forEach(u=>{e.includes(u.agentId)&&s.set(u.agentId,(s.get(u.agentId)||0)+1)});const o=[...s.entries()].sort(([,u],[,m])=>u-m).slice(0,Math.ceil(e.length/2));return o[Math.floor(Math.random()*o.length)][0]}}async function fp(e,t,n){if(!e.llmConfig)throw new Error(`主持人 ${e.name} 未配置LLM，无法生成总结`);try{const r=`作为讨论主持人，请对以下最近的讨论内容进行简洁总结：

讨论主题：${t.topic}
最近消息：
${n.map(s=>`- ${s.content}`).join(`
`)}

请提供一个简洁的总结，突出关键观点和进展：`;return await Yt.generateResponse(e.llmConfig,r)}catch(r){return console.error("主持人总结生成失败:",r),`总结生成失败：${r instanceof Error?r.message:"未知错误"}`}}async function mp(e,t,n){if(!e.llmConfig)return .8;try{const r=`作为讨论主持人，请评估以下讨论内容与主题的相关性：

讨论主题：${t.topic}
最近消息：
${n.map(o=>`- ${o.content}`).join(`
`)}

请给出相关性评分（0-1之间的数字，1表示完全相关，0表示完全无关）。
只需要返回数字，不需要解释：`,s=await Yt.generateResponse(e.llmConfig,r),i=parseFloat(s.trim());return isNaN(i)?.8:Math.max(0,Math.min(1,i))}catch(r){return console.error("话题相关性计算失败:",r),.8}}async function pp(e,t,n,r){if(!e.llmConfig||n.length===0)return qr(n.map(s=>s.id),r,t.mode);try{const s=n.map(m=>`${m.name}（专业：${m.expertise.join("、")}，思维：${m.thinkingStyle}）`).join(`
`),i=`作为讨论主持人，请根据以下信息选择下一个最适合发言的参与者：

讨论主题：${t.topic}
参与者信息：
${s}

最近讨论内容：
${r.slice(-5).map(m=>{const x=n.find(y=>y.id===m.agentId);return`${(x==null?void 0:x.name)||"未知"}: ${m.content}`}).join(`
`)}

请选择最适合继续这个话题的参与者，只需要返回参与者的名字：`,a=(await Yt.generateResponse(e.llmConfig,i)).trim(),u=n.find(m=>m.name===a);return(u==null?void 0:u.id)||qr(n.map(m=>m.id),r,t.mode)}catch(s){return console.error("智能发言人选择失败:",s),qr(n.map(i=>i.id),r,t.mode)}}async function hp(e,t,n){if(!e.llmConfig)return{off_topic:"让我们回到主题上来，继续讨论相关内容。",low_activity:"大家可以分享更多想法，让讨论更加活跃。",conflict_resolution:"我们来总结一下不同的观点，寻找共同点。",summary_request:"让我总结一下到目前为止的讨论要点。"}[n];try{const r={off_topic:`作为讨论主持人，发现讨论偏离了主题"${t.topic}"。请生成一段引导语，礼貌地将讨论拉回正轨。`,low_activity:"作为讨论主持人，发现讨论活跃度较低。请生成一段鼓励性的引导语，激发参与者的讨论热情。",conflict_resolution:"作为讨论主持人，发现参与者之间存在分歧。请生成一段中性的引导语，帮助化解冲突并推进讨论。",summary_request:"作为讨论主持人，需要对当前讨论进行阶段性总结。请生成一段总结性的引导语。"};return(await Yt.generateResponse(e.llmConfig,r[n])).trim()}catch(r){return console.error("主持人引导语生成失败:",r),"让我们继续讨论，保持专注和建设性。"}}async function gp(e,t,n){if(t.consensusScore>85)return{shouldTerminate:!0,reason:"已达成高度共识"};if(t.moderatorInterventions>=n.maxInterventions&&n.maxInterventions>0)return{shouldTerminate:!0,reason:"已达到最大干预次数"};if(!n.autoTerminate||!e.llmConfig)return{shouldTerminate:!1};try{const r=t.messages.slice(-10),s=`作为讨论主持人，请判断以下讨论是否应该结束：

讨论主题：${t.topic}
当前共识度：${t.consensusScore}%
话题相关性：${t.topicRelevanceScore}
干预次数：${t.moderatorInterventions}

最近讨论内容：
${r.map(a=>`- ${a.content}`).join(`
`)}

请判断是否应该结束讨论，只需要回答"是"或"否"：`,o=(await Yt.generateResponse(e.llmConfig,s)).trim().toLowerCase().includes("是");return{shouldTerminate:o,reason:o?"主持人判断讨论已充分进行":void 0}}catch(r){return console.error("讨论终止判断失败:",r),{shouldTerminate:!1}}}function xp(){const{state:e,dispatch:t,endDiscussion:n,sendMessage:r}=Lt(),[s,i]=T.useState(!1),[o,a]=T.useState({totalMessages:0,consensusScore:0,activeTime:0}),[u,m]=T.useState(""),[x,y]=T.useState(1),[v,w]=T.useState(0),j=T.useRef(null),p=T.useRef(null),g=T.useRef(null),{currentDiscussion:c}=e;T.useEffect(()=>(c&&c.status==="active"&&(i(!0),d()),()=>{p.current&&clearInterval(p.current),g.current&&clearTimeout(g.current)}),[c]),T.useEffect(()=>{var S;(S=j.current)==null||S.scrollIntoView({behavior:"smooth"})},[c==null?void 0:c.messages]),T.useEffect(()=>{if(c){const S=up(c.messages,e.agents);if(a({totalMessages:c.messages.length,consensusScore:S,activeTime:Math.floor((Date.now()-new Date(c.createdAt).getTime())/1e3)}),t({type:"UPDATE_CONSENSUS",payload:{consensusScore:S}}),S>80&&c.status==="active"){const b=cp(c.messages,c.topic);t({type:"UPDATE_CONSENSUS",payload:{consensusScore:S,consensus:b}}),i(!1)}}},[c==null?void 0:c.messages]);const d=()=>{if(!c||c.status!=="active")return;const S=c.participants,b=e.agents.filter(he=>S.includes(he.id)),A=c.moderatorId?e.agents.find(he=>he.id===c.moderatorId):null,M=async()=>{if(!c||c.status!=="active"){i(!1);return}if(A&&A.moderatorConfig){const Ie=A.moderatorConfig;if(c.messages.length-v>=Ie.summaryFrequency)try{const q=await fp(A,c,c.messages.slice(-Ie.summaryFrequency));m(q),w(c.messages.length)}catch(q){console.error("主持人总结生成失败:",q)}if(c.messages.length>0)try{const q=await mp(A,c,c.messages.slice(-3));if(y(q),q<Ie.interventionThreshold){const Ln=await hp(A,c,"off_topic");r(Ln,A.id,"statement"),t({type:"UPDATE_CONSENSUS",payload:{consensusScore:c.consensusScore,moderatorInterventions:(c.moderatorInterventions||0)+1}});const E=Math.random()*3e3+2e3;g.current=setTimeout(M,E);return}}catch(q){console.error("话题相关性检查失败:",q)}try{const q=await gp(A,c,Ie);if(q.shouldTerminate){console.log("主持人决定终止讨论:",q.reason),n();return}}catch(q){console.error("讨论终止检查失败:",q)}}let he;if(A&&c.mode==="moderator"?he=await pp(A,c,b,c.messages):he=qr(S,c.messages,c.mode),he){const Ie=b.find(Ge=>Ge.id===he);if(Ie)try{const Ge=await ap(Ie,c,c.topic,c.messages.slice(-5)),q=f(Ge);r(Ge,Ie.id,q)}catch(Ge){console.error("生成消息失败:",Ge)}}const Tt=Math.random()*3e3+2e3;g.current=setTimeout(M,Tt)},je=Math.random()*2e3+1e3;g.current=setTimeout(M,je)},f=S=>S.includes("我赞同")||S.includes("我同意")||S.includes("这个想法很好")?"agreement":S.includes("但是")||S.includes("我认为")||S.includes("不同的看法")?"disagreement":S.includes("？")||S.includes("我们")||S.includes("如何")?"question":"statement",h=()=>{i(!1),g.current&&clearTimeout(g.current),n()},N=S=>{const b=Math.floor(S/60),A=S%60;return`${b}:${A.toString().padStart(2,"0")}`};if(!c)return l.jsx("div",{className:"h-full bg-gradient-to-br from-gray-50 to-blue-50 w-full overflow-y-auto",children:l.jsx("div",{className:"discussion-container",children:l.jsxs("div",{className:"bg-white rounded-xl shadow-lg p-8 text-center max-w-2xl",children:[l.jsx(et,{size:64,className:"text-gray-400 mx-auto mb-4"}),l.jsx("h2",{className:"text-2xl font-bold text-gray-900 mb-4",children:"没有进行中的讨论"}),l.jsx("p",{className:"text-gray-600",children:"请先创建一个新的讨论来开始智能体对话。"})]})})});const C=e.agents.filter(S=>c.participants.includes(S.id));return l.jsx("div",{className:"h-screen bg-gradient-to-br from-blue-50 to-indigo-50 w-full overflow-hidden pt-16",children:l.jsx("div",{className:"discussion-container h-full",children:l.jsxs("div",{className:"discussion-content h-full",children:[l.jsx("div",{className:"fixed-size-discussion flex-shrink-0 h-[70vh]",children:l.jsxs("div",{className:"bg-white rounded-xl shadow-lg overflow-hidden h-full flex flex-col",children:[l.jsxs("div",{className:"bg-gradient-to-r from-blue-600 to-indigo-600 text-white p-6",children:[l.jsxs("div",{className:"flex items-center justify-between mb-4",children:[l.jsxs("div",{className:"flex items-center gap-3",children:[l.jsx(et,{size:32}),l.jsxs("div",{children:[l.jsx("h1",{className:"text-2xl font-bold",children:"讨论进行中"}),l.jsx("p",{className:"text-blue-100",children:c.mode==="free"?"自由讨论模式":"主持人模式"})]})]}),l.jsxs("div",{className:"flex items-center gap-4",children:[l.jsxs("div",{className:"text-center",children:[l.jsx("div",{className:"text-2xl font-bold",children:N(o.activeTime)}),l.jsx("div",{className:"text-sm text-blue-100",children:"讨论时长"})]}),l.jsxs("button",{onClick:h,className:"flex items-center gap-2 bg-red-500 hover:bg-red-600 px-4 py-2 rounded-lg transition-colors",children:[l.jsx(Km,{size:20}),"结束讨论"]})]})]}),l.jsxs("div",{className:"bg-white bg-opacity-20 rounded-lg p-4",children:[l.jsx("h3",{className:"font-semibold mb-2",children:"讨论话题"}),l.jsx("p",{className:"text-blue-50",children:c.topic})]})]}),l.jsxs("div",{className:"h-[calc(100%-180px)] overflow-y-auto p-6 space-y-4",children:[c.messages.map(S=>l.jsx(yp,{message:S,agent:C.find(b=>b.id===S.agentId)},S.id)),s&&l.jsxs("div",{className:"flex items-center gap-3 text-gray-500",children:[l.jsx("div",{className:"animate-spin w-5 h-5 border-2 border-blue-500 border-t-transparent rounded-full"}),l.jsx("span",{children:"智能体正在思考中..."})]}),l.jsx("div",{ref:j})]})]})}),l.jsxs("div",{className:"space-y-6 fixed-size-sidebar",children:[c.moderatorId&&(()=>{const S=e.agents.find(b=>b.id===c.moderatorId);return S?l.jsxs("div",{className:"bg-white rounded-xl shadow-lg p-6",children:[l.jsxs("div",{className:"flex items-center gap-3 mb-4",children:[l.jsx("div",{className:"w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center",children:l.jsx(Qt,{size:16,className:"text-purple-600"})}),l.jsx("h3",{className:"font-semibold text-gray-900",children:"主持人"})]}),l.jsxs("div",{className:"space-y-4",children:[l.jsxs("div",{className:"flex items-center gap-3",children:[l.jsx("img",{src:S.avatar,alt:S.name,className:"w-10 h-10 rounded-full object-cover"}),l.jsxs("div",{children:[l.jsx("h4",{className:"font-medium text-gray-900",children:S.name}),l.jsx("p",{className:"text-sm text-gray-500",children:S.expertise.slice(0,2).join("、")})]})]}),l.jsxs("div",{className:"space-y-2",children:[l.jsxs("div",{className:"flex items-center justify-between",children:[l.jsx("span",{className:"text-sm font-medium text-gray-700",children:"话题相关性"}),l.jsxs("span",{className:"text-sm font-bold text-gray-900",children:[Math.round(x*100),"%"]})]}),l.jsx("div",{className:"w-full h-2 bg-gray-200 rounded-full overflow-hidden",children:l.jsx("div",{className:`h-full transition-all duration-500 ${x>.8?"bg-green-500":x>.6?"bg-yellow-500":"bg-red-500"}`,style:{width:`${x*100}%`}})})]}),u&&l.jsxs("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-3",children:[l.jsx("h5",{className:"text-sm font-medium text-blue-900 mb-2",children:"最新总结"}),l.jsx("p",{className:"text-sm text-blue-700",children:u})]}),l.jsxs("div",{className:"flex items-center justify-between text-sm",children:[l.jsx("span",{className:"text-gray-600",children:"干预次数"}),l.jsx("span",{className:"font-medium text-gray-900",children:c.moderatorInterventions||0})]})]})]}):null})(),l.jsxs("div",{className:"bg-white rounded-xl shadow-lg p-6",children:[l.jsxs("div",{className:"flex items-center gap-3 mb-4",children:[l.jsx(Bc,{size:24,className:"text-green-600"}),l.jsx("h3",{className:"font-semibold text-gray-900",children:"共识度"})]}),l.jsxs("div",{className:"space-y-4",children:[l.jsxs("div",{className:"relative",children:[l.jsx("div",{className:"w-full h-4 bg-gray-200 rounded-full overflow-hidden",children:l.jsx("div",{className:`h-full transition-all duration-500 ${o.consensusScore>80?"bg-green-500":o.consensusScore>60?"bg-yellow-500":"bg-red-500"}`,style:{width:`${o.consensusScore}%`}})}),l.jsxs("div",{className:"text-center mt-2 font-bold text-2xl",children:[Math.round(o.consensusScore),"%"]})]}),c.status==="consensus"&&l.jsxs("div",{className:"flex items-center gap-2 bg-green-50 text-green-800 p-3 rounded-lg",children:[l.jsx(kn,{size:20}),l.jsx("span",{className:"font-medium",children:"已达成共识！"})]})]})]}),l.jsxs("div",{className:"bg-white rounded-xl shadow-lg p-6",children:[l.jsxs("div",{className:"flex items-center gap-3 mb-4",children:[l.jsx(Qt,{size:24,className:"text-blue-600"}),l.jsx("h3",{className:"font-semibold text-gray-900",children:"参与者"})]}),l.jsx("div",{className:"space-y-3",children:C.map(S=>{const b=c.messages.filter(M=>M.agentId===S.id).length,A=c.messages.slice().reverse().find(M=>M.agentId===S.id);return l.jsxs("div",{className:"flex items-center gap-3 p-3 bg-gray-50 rounded-lg",children:[l.jsx("img",{src:S.avatar,alt:S.name,className:"w-10 h-10 rounded-full object-cover"}),l.jsxs("div",{className:"flex-1",children:[l.jsx("div",{className:"font-medium text-gray-900",children:S.name}),l.jsxs("div",{className:"text-sm text-gray-500",children:[b," 条消息"]})]}),A&&l.jsx("div",{className:"text-xs text-gray-400",children:new Date(A.timestamp).toLocaleTimeString()})]},S.id)})})]}),l.jsxs("div",{className:"bg-white rounded-xl shadow-lg p-6",children:[l.jsxs("div",{className:"flex items-center gap-3 mb-4",children:[l.jsx(Am,{size:24,className:"text-purple-600"}),l.jsx("h3",{className:"font-semibold text-gray-900",children:"讨论统计"})]}),l.jsxs("div",{className:"space-y-3",children:[l.jsxs("div",{className:"flex justify-between items-center",children:[l.jsx("span",{className:"text-gray-600",children:"总消息数"}),l.jsx("span",{className:"font-bold text-lg",children:o.totalMessages})]}),l.jsxs("div",{className:"flex justify-between items-center",children:[l.jsx("span",{className:"text-gray-600",children:"讨论时长"}),l.jsx("span",{className:"font-bold text-lg",children:N(o.activeTime)})]}),l.jsxs("div",{className:"flex justify-between items-center",children:[l.jsx("span",{className:"text-gray-600",children:"参与者数量"}),l.jsx("span",{className:"font-bold text-lg",children:C.length})]})]})]}),c.consensus&&l.jsxs("div",{className:"bg-green-50 border border-green-200 rounded-xl p-6",children:[l.jsxs("div",{className:"flex items-center gap-3 mb-3",children:[l.jsx(kn,{size:24,className:"text-green-600"}),l.jsx("h3",{className:"font-semibold text-green-900",children:"讨论结论"})]}),l.jsx("p",{className:"text-green-800",children:c.consensus})]})]})]})})})}function yp({message:e,agent:t}){const n=()=>{switch(e.type){case"question":return l.jsx(Fm,{size:16,className:"text-blue-500"});case"agreement":return l.jsx(Xm,{size:16,className:"text-green-500"});case"disagreement":return l.jsx(Ym,{size:16,className:"text-red-500"});default:return l.jsx(et,{size:16,className:"text-gray-500"})}},r=()=>{switch(e.type){case"question":return"border-l-blue-500";case"agreement":return"border-l-green-500";case"disagreement":return"border-l-red-500";default:return"border-l-gray-300"}};return l.jsxs("div",{className:`flex gap-4 p-4 bg-gray-50 rounded-lg border-l-4 ${r()}`,children:[l.jsx("img",{src:t==null?void 0:t.avatar,alt:t==null?void 0:t.name,className:"w-12 h-12 rounded-full object-cover flex-shrink-0"}),l.jsxs("div",{className:"flex-1",children:[l.jsxs("div",{className:"flex items-center gap-2 mb-2",children:[l.jsx("span",{className:"font-semibold text-gray-900",children:t==null?void 0:t.name}),n(),l.jsx("span",{className:"text-xs text-gray-500",children:new Date(e.timestamp).toLocaleTimeString()})]}),l.jsx("p",{className:"text-gray-800 leading-relaxed",children:e.content})]})]})}const vp=({isOpen:e,onClose:t,onSave:n,editingConfig:r})=>{const[s,i]=T.useState({name:"",provider:"openai",model:"",apiKey:"",baseURL:"",temperature:.7,maxTokens:1e3,systemPrompt:""}),[o,a]=T.useState(!1),[u,m]=T.useState(!1),[x,y]=T.useState(null),[v,w]=T.useState([]),[j,p]=T.useState("");T.useEffect(()=>{r?(i(r),p("")):(i({name:"",provider:"openai",model:"",apiKey:"",baseURL:"",temperature:.7,maxTokens:1e3,systemPrompt:""}),p("")),y(null),w([])},[r,e]);const g=h=>{if(p(h),h){const N=ka.find(C=>C.id===h);N&&i(C=>({...C,name:N.name,provider:N.provider.toLowerCase(),model:N.model,temperature:N.defaultSettings.temperature,maxTokens:N.defaultSettings.maxTokens}))}},c=(h,N)=>{if(i(C=>({...C,[h]:N})),y(null),v.length>0){const C=R.validateLLMConfig({...s,[h]:N});w(C)}},d=async()=>{const h=R.validateLLMConfig(s);if(h.length>0){w(h);return}m(!0),y(null);try{const N={id:"test",name:s.name,provider:s.provider,model:s.model,apiKey:s.apiKey,baseURL:s.baseURL,temperature:s.temperature,maxTokens:s.maxTokens,systemPrompt:s.systemPrompt},C=await Yt.testLLMConfig(N);y({success:C,message:C?"连接测试成功！":"连接测试失败，请检查配置。"})}catch(N){y({success:!1,message:`连接测试失败: ${N instanceof Error?N.message:"未知错误"}`})}finally{m(!1)}},f=()=>{const h=R.validateLLMConfig(s);if(h.length>0){w(h);return}const N={id:(r==null?void 0:r.id)||R.generateLLMConfigId(),name:s.name,provider:s.provider,model:s.model,apiKey:s.apiKey,baseURL:s.baseURL,temperature:s.temperature,maxTokens:s.maxTokens,systemPrompt:s.systemPrompt};n(N),t()};return e?l.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:l.jsxs("div",{className:"bg-white rounded-lg p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto",children:[l.jsxs("div",{className:"flex justify-between items-center mb-6",children:[l.jsx("h2",{className:"text-xl font-bold",children:r?"编辑LLM配置":"新建LLM配置"}),l.jsx("button",{onClick:t,className:"text-gray-500 hover:text-gray-700",children:l.jsx(Jm,{className:"w-6 h-6"})})]}),!r&&l.jsxs("div",{className:"mb-6",children:[l.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"选择预设配置（可选）"}),l.jsxs("select",{value:j,onChange:h=>g(h.target.value),className:"w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:[l.jsx("option",{value:"",children:"自定义配置"}),ka.map(h=>l.jsxs("option",{value:h.id,children:[h.name," - ",h.description]},h.id))]})]}),v.length>0&&l.jsxs("div",{className:"mb-4 p-3 bg-red-50 border border-red-200 rounded-md",children:[l.jsxs("div",{className:"flex items-center",children:[l.jsx(Sn,{className:"w-5 h-5 text-red-500 mr-2"}),l.jsx("span",{className:"text-red-700 font-medium",children:"配置错误"})]}),l.jsx("ul",{className:"mt-2 text-sm text-red-600",children:v.map((h,N)=>l.jsxs("li",{children:["• ",h]},N))})]}),x&&l.jsx("div",{className:`mb-4 p-3 border rounded-md ${x.success?"bg-green-50 border-green-200":"bg-red-50 border-red-200"}`,children:l.jsxs("div",{className:"flex items-center",children:[x.success?l.jsx(kn,{className:"w-5 h-5 text-green-500 mr-2"}):l.jsx(Sn,{className:"w-5 h-5 text-red-500 mr-2"}),l.jsx("span",{className:`font-medium ${x.success?"text-green-700":"text-red-700"}`,children:x.message})]})}),l.jsxs("div",{className:"space-y-4",children:[l.jsxs("div",{children:[l.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"配置名称 *"}),l.jsx("input",{type:"text",value:s.name||"",onChange:h=>c("name",h.target.value),className:"w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"例如：我的GPT-4配置"})]}),l.jsxs("div",{children:[l.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"提供商 *"}),l.jsxs("select",{value:s.provider||"openai",onChange:h=>c("provider",h.target.value),className:"w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:[l.jsx("option",{value:"openai",children:"OpenAI"}),l.jsx("option",{value:"anthropic",children:"Anthropic"}),l.jsx("option",{value:"azure",children:"Azure OpenAI"}),l.jsx("option",{value:"custom",children:"自定义"})]})]}),l.jsxs("div",{children:[l.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"模型名称 *"}),l.jsx("input",{type:"text",value:s.model||"",onChange:h=>c("model",h.target.value),className:"w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"例如：gpt-4, claude-3-opus-20240229"})]}),l.jsxs("div",{children:[l.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"API密钥 *"}),l.jsxs("div",{className:"relative",children:[l.jsx("input",{type:o?"text":"password",value:s.apiKey||"",onChange:h=>c("apiKey",h.target.value),className:"w-full p-2 pr-10 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"输入API密钥"}),l.jsx("button",{type:"button",onClick:()=>a(!o),className:"absolute right-2 top-2 text-gray-500 hover:text-gray-700",children:o?l.jsx(Rm,{className:"w-5 h-5"}):l.jsx($m,{className:"w-5 h-5"})})]})]}),(s.provider==="azure"||s.provider==="custom")&&l.jsxs("div",{children:[l.jsxs("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:["基础URL ",s.provider==="azure"?"*":"(可选)"]}),l.jsx("input",{type:"text",value:s.baseURL||"",onChange:h=>c("baseURL",h.target.value),className:"w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:s.provider==="azure"?"https://your-resource.openai.azure.com":"https://api.example.com"})]}),l.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[l.jsxs("div",{children:[l.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"温度 (0-2)"}),l.jsx("input",{type:"number",min:"0",max:"2",step:"0.1",value:s.temperature||.7,onChange:h=>c("temperature",parseFloat(h.target.value)),className:"w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]}),l.jsxs("div",{children:[l.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"最大令牌数"}),l.jsx("input",{type:"number",min:"1",max:"4000",value:s.maxTokens||1e3,onChange:h=>c("maxTokens",parseInt(h.target.value)),className:"w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]})]}),l.jsxs("div",{children:[l.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"自定义系统提示词 (可选)"}),l.jsx("textarea",{value:s.systemPrompt||"",onChange:h=>c("systemPrompt",h.target.value),rows:3,className:"w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"添加额外的系统提示词..."})]})]}),l.jsxs("div",{className:"flex justify-between mt-6",children:[l.jsxs("button",{onClick:d,disabled:u,className:"flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed",children:[l.jsx(Gm,{className:"w-4 h-4 mr-2"}),u?"测试中...":"测试连接"]}),l.jsxs("div",{className:"flex space-x-3",children:[l.jsx("button",{onClick:t,className:"px-4 py-2 text-gray-600 border border-gray-300 rounded-md hover:bg-gray-50",children:"取消"}),l.jsxs("button",{onClick:f,className:"flex items-center px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700",children:[l.jsx(Wm,{className:"w-4 h-4 mr-2"}),"保存"]})]})]})]})}):null},wp=()=>{const[e,t]=T.useState([]),[n,r]=T.useState(!1),[s,i]=T.useState(null),[o,a]=T.useState(!1),[u,m]=T.useState(!0),[x,y]=T.useState({total:0,byProvider:{},recentlyUsed:[]});T.useEffect(()=>{v()},[]);const v=async()=>{try{m(!0);const[f,h]=await Promise.all([R.getLLMConfigs(),R.getLLMConfigStats()]);t(f),y(h)}catch(f){console.error("Failed to load configs:",f),t([]),y({total:0,byProvider:{},recentlyUsed:[]})}finally{m(!1)}},w=async f=>{try{await R.saveLLMConfig(f),await v()}catch{alert("保存配置失败")}},j=f=>{i(f),r(!0)},p=async f=>{if(confirm("确定要删除这个LLM配置吗？"))try{await R.deleteLLMConfig(f),await v()}catch{alert("删除配置失败")}},g=()=>{i(null),r(!0)},c=async()=>{try{const f=await R.exportLLMConfigs(),h=new Blob([f],{type:"application/json"}),N=URL.createObjectURL(h),C=document.createElement("a");C.href=N,C.download=`llm-configs-${new Date().toISOString().split("T")[0]}.json`,document.body.appendChild(C),C.click(),document.body.removeChild(C),URL.revokeObjectURL(N)}catch{alert("导出失败")}},d=f=>{var C;const h=(C=f.target.files)==null?void 0:C[0];if(!h)return;const N=new FileReader;N.onload=async S=>{var b;try{const A=(b=S.target)==null?void 0:b.result,M=await R.importLLMConfigs(A);M.success>0&&(alert(`成功导入 ${M.success} 个配置`),await v()),M.errors.length>0&&alert(`导入时遇到错误：
${M.errors.join(`
`)}`)}catch{alert("导入失败：文件格式错误")}},N.readAsText(h),f.target.value=""};return u?l.jsx("div",{className:"h-full overflow-y-auto",children:l.jsx("div",{className:"centered-container",children:l.jsx("div",{className:"centered-content",children:l.jsx("div",{className:"flex items-center justify-center h-64",children:l.jsxs("div",{className:"text-center",children:[l.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"}),l.jsx("p",{className:"text-gray-600",children:"正在加载LLM配置..."})]})})})})}):l.jsx("div",{className:"h-full overflow-y-auto",children:l.jsx("div",{className:"centered-container",children:l.jsxs("div",{className:"centered-content",children:[l.jsxs("div",{className:"flex justify-between items-center mb-6",children:[l.jsxs("div",{children:[l.jsxs("h2",{className:"text-2xl font-bold text-gray-900 flex items-center",children:[l.jsx(jt,{className:"w-8 h-8 mr-3 text-blue-600"}),"LLM配置管理"]}),l.jsx("p",{className:"text-gray-600 mt-1",children:"管理大语言模型配置，为智能体提供AI能力"})]}),l.jsxs("div",{className:"flex space-x-3",children:[l.jsxs("button",{onClick:()=>a(!o),className:"flex items-center px-4 py-2 text-gray-600 border border-gray-300 rounded-md hover:bg-gray-50",children:[l.jsx(Nt,{className:"w-4 h-4 mr-2"}),"导入/导出"]}),l.jsxs("button",{onClick:g,className:"flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700",children:[l.jsx(fi,{className:"w-4 h-4 mr-2"}),"新建配置"]})]})]}),l.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 mb-6",children:[l.jsxs("div",{className:"bg-white p-4 rounded-lg border border-gray-200",children:[l.jsx("div",{className:"text-2xl font-bold text-blue-600",children:x.total}),l.jsx("div",{className:"text-sm text-gray-600",children:"总配置数"})]}),l.jsxs("div",{className:"bg-white p-4 rounded-lg border border-gray-200",children:[l.jsx("div",{className:"text-2xl font-bold text-green-600",children:Object.keys(x.byProvider).length}),l.jsx("div",{className:"text-sm text-gray-600",children:"支持的提供商"})]}),l.jsxs("div",{className:"bg-white p-4 rounded-lg border border-gray-200",children:[l.jsx("div",{className:"text-2xl font-bold text-purple-600",children:x.recentlyUsed.length}),l.jsx("div",{className:"text-sm text-gray-600",children:"最近使用"})]})]}),o&&l.jsxs("div",{className:"bg-gray-50 p-4 rounded-lg mb-6 border border-gray-200",children:[l.jsx("h3",{className:"text-lg font-medium mb-3",children:"导入/导出配置"}),l.jsxs("div",{className:"flex space-x-4",children:[l.jsxs("button",{onClick:c,className:"flex items-center px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700",children:[l.jsx(di,{className:"w-4 h-4 mr-2"}),"导出配置"]}),l.jsxs("label",{className:"flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 cursor-pointer",children:[l.jsx(mi,{className:"w-4 h-4 mr-2"}),"导入配置",l.jsx("input",{type:"file",accept:".json",onChange:d,className:"hidden"})]})]}),l.jsx("p",{className:"text-sm text-gray-600 mt-2",children:"导出的配置文件会隐藏API密钥，导入时需要重新设置"})]}),l.jsx("div",{className:"bg-white rounded-lg border border-gray-200",children:e.length===0?l.jsxs("div",{className:"p-8 text-center",children:[l.jsx(jt,{className:"w-16 h-16 text-gray-300 mx-auto mb-4"}),l.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"还没有LLM配置"}),l.jsx("p",{className:"text-gray-600 mb-4",children:"创建第一个LLM配置来为智能体提供AI能力"}),l.jsxs("button",{onClick:g,className:"flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 mx-auto",children:[l.jsx(fi,{className:"w-4 h-4 mr-2"}),"新建配置"]})]}):l.jsx("div",{className:"divide-y divide-gray-200",children:e.map(f=>l.jsxs("div",{className:"p-4 hover:bg-gray-50",children:[l.jsxs("div",{className:"flex items-center justify-between",children:[l.jsxs("div",{className:"flex items-center space-x-4",children:[l.jsx("div",{className:"text-2xl",children:Ss(f.provider)}),l.jsxs("div",{children:[l.jsx("h3",{className:"text-lg font-medium text-gray-900",children:f.name}),l.jsxs("div",{className:"flex items-center space-x-2 mt-1",children:[l.jsx("span",{className:`px-2 py-1 text-xs rounded-full ${ks(f.provider)}`,children:f.provider.toUpperCase()}),l.jsx("span",{className:"text-sm text-gray-600",children:f.model})]})]})]}),l.jsxs("div",{className:"flex items-center space-x-2",children:[l.jsxs("div",{className:"text-right text-sm text-gray-600",children:[l.jsxs("div",{children:["温度: ",f.temperature]}),l.jsxs("div",{children:["令牌: ",f.maxTokens]})]}),l.jsxs("div",{className:"flex space-x-1",children:[l.jsx("button",{onClick:()=>j(f),className:"p-2 text-gray-600 hover:text-blue-600 hover:bg-blue-50 rounded-md",title:"编辑配置",children:l.jsx(Hc,{className:"w-4 h-4"})}),l.jsx("button",{onClick:()=>p(f.id),className:"p-2 text-gray-600 hover:text-red-600 hover:bg-red-50 rounded-md",title:"删除配置",children:l.jsx(mr,{className:"w-4 h-4"})})]})]})]}),f.systemPrompt&&l.jsxs("div",{className:"mt-3 p-3 bg-gray-50 rounded-md",children:[l.jsx("div",{className:"text-sm text-gray-600",children:l.jsx("strong",{children:"自定义系统提示词:"})}),l.jsx("div",{className:"text-sm text-gray-800 mt-1 line-clamp-2",children:f.systemPrompt})]})]},f.id))})}),Object.keys(x.byProvider).length>0&&l.jsxs("div",{className:"mt-6 bg-white p-4 rounded-lg border border-gray-200",children:[l.jsx("h3",{className:"text-lg font-medium mb-3",children:"提供商分布"}),l.jsx("div",{className:"flex flex-wrap gap-2",children:Object.entries(x.byProvider).map(([f,h])=>l.jsxs("div",{className:`flex items-center px-3 py-1 rounded-full text-sm ${ks(f)}`,children:[l.jsx("span",{className:"mr-1",children:Ss(f)}),f.toUpperCase(),": ",h]},f))})]}),l.jsx(vp,{isOpen:n,onClose:()=>{r(!1),i(null)},onSave:w,editingConfig:s})]})})})},jp=()=>{const{state:e,exportData:t,importData:n,clearAllData:r}=Lt(),[s,i]=T.useState(!1),[o,a]=T.useState(!1),[u,m]=T.useState(null),[x,y]=T.useState(!1),[v,w]=T.useState(null);T.useEffect(()=>{(async()=>{try{const f=await R.getStorageInfo();w(f)}catch(f){console.error("Failed to fetch storage info:",f),w({used:0,available:0,total:0})}})()},[]);const j=d=>{if(d===0)return"0 Bytes";const f=1024,h=["Bytes","KB","MB","GB"],N=Math.floor(Math.log(d)/Math.log(f));return parseFloat((d/Math.pow(f,N)).toFixed(2))+" "+h[N]},p=async()=>{i(!0);try{const d=await t(),f=new Blob([d],{type:"application/json"}),h=URL.createObjectURL(f),N=document.createElement("a");N.href=h,N.download=`multi-agent-system-backup-${new Date().toISOString().split("T")[0]}.json`,document.body.appendChild(N),N.click(),document.body.removeChild(N),URL.revokeObjectURL(h)}catch(d){m({success:!1,message:"导出失败: "+(d instanceof Error?d.message:"未知错误")})}finally{i(!1)}},g=async d=>{var h;const f=(h=d.target.files)==null?void 0:h[0];if(f){a(!0),m(null);try{const N=await f.text(),C=await n(N);m({success:C,message:C?"数据导入成功！":"数据导入失败，请检查文件格式。"})}catch(N){m({success:!1,message:"导入失败: "+(N instanceof Error?N.message:"未知错误")})}finally{a(!1),d.target.value=""}}},c=async()=>{try{await r(),y(!1),m({success:!0,message:"所有数据已清除，系统已重置为默认状态。"})}catch(d){m({success:!1,message:"清除数据失败: "+(d instanceof Error?d.message:"未知错误")})}};return l.jsx("div",{className:"h-full overflow-y-auto",children:l.jsx("div",{className:"centered-container",children:l.jsxs("div",{className:"centered-content",children:[l.jsx("div",{className:"flex justify-between items-center mb-6",children:l.jsxs("div",{children:[l.jsxs("h2",{className:"text-2xl font-bold text-gray-900 flex items-center",children:[l.jsx(uo,{className:"w-8 h-8 mr-3 text-blue-600"}),"数据管理"]}),l.jsx("p",{className:"text-gray-600 mt-1",children:"管理系统数据的备份、恢复和清理"})]})}),u&&l.jsx("div",{className:`mb-6 p-4 rounded-lg border ${u.success?"bg-green-50 border-green-200":"bg-red-50 border-red-200"}`,children:l.jsxs("div",{className:"flex items-center",children:[u.success?l.jsx(kn,{className:"w-5 h-5 text-green-500 mr-2"}):l.jsx(ci,{className:"w-5 h-5 text-red-500 mr-2"}),l.jsx("span",{className:`font-medium ${u.success?"text-green-700":"text-red-700"}`,children:u.message})]})}),l.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 mb-8",children:[l.jsx("div",{className:"bg-white p-4 rounded-lg border border-gray-200",children:l.jsxs("div",{className:"flex items-center justify-between",children:[l.jsxs("div",{children:[l.jsx("div",{className:"text-2xl font-bold text-blue-600",children:e.agents.length}),l.jsx("div",{className:"text-sm text-gray-600",children:"智能体"})]}),l.jsx(Nt,{className:"w-8 h-8 text-blue-500"})]})}),l.jsx("div",{className:"bg-white p-4 rounded-lg border border-gray-200",children:l.jsxs("div",{className:"flex items-center justify-between",children:[l.jsxs("div",{children:[l.jsx("div",{className:"text-2xl font-bold text-green-600",children:e.allDiscussions.length}),l.jsx("div",{className:"text-sm text-gray-600",children:"历史讨论"})]}),l.jsx(Om,{className:"w-8 h-8 text-green-500"})]})}),l.jsx("div",{className:"bg-white p-4 rounded-lg border border-gray-200",children:l.jsxs("div",{className:"flex items-center justify-between",children:[l.jsxs("div",{children:[l.jsx("div",{className:"text-2xl font-bold text-purple-600",children:v?j(v.used):"加载中..."}),l.jsx("div",{className:"text-sm text-gray-600",children:"已用存储"})]}),l.jsx(Um,{className:"w-8 h-8 text-purple-500"})]})})]}),l.jsxs("div",{className:"bg-white p-6 rounded-lg border border-gray-200 mb-8",children:[l.jsx("h3",{className:"text-lg font-medium mb-4",children:"存储使用情况"}),v?l.jsxs("div",{className:"space-y-3",children:[l.jsxs("div",{className:"flex justify-between text-sm",children:[l.jsx("span",{children:"已使用"}),l.jsx("span",{children:j(v.used)})]}),l.jsx("div",{className:"w-full bg-gray-200 rounded-full h-2",children:l.jsx("div",{className:"bg-blue-500 h-2 rounded-full transition-all",style:{width:`${v.used/v.total*100}%`}})}),l.jsxs("div",{className:"flex justify-between text-sm text-gray-600",children:[l.jsxs("span",{children:["可用: ",j(v.available)]}),l.jsxs("span",{children:["总计: ",j(v.total)]})]})]}):l.jsx("div",{className:"text-center text-gray-500",children:"加载存储信息中..."})]}),l.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[l.jsxs("div",{className:"bg-white p-6 rounded-lg border border-gray-200",children:[l.jsxs("div",{className:"flex items-center mb-4",children:[l.jsx(di,{className:"w-6 h-6 text-green-600 mr-3"}),l.jsx("h3",{className:"text-lg font-medium",children:"导出数据"})]}),l.jsx("p",{className:"text-gray-600 mb-4 text-sm",children:"将所有配置和历史记录导出为JSON文件，用于备份或迁移。"}),l.jsxs("button",{onClick:p,disabled:s,className:"w-full flex items-center justify-center px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed",children:[l.jsx(di,{className:"w-4 h-4 mr-2"}),s?"导出中...":"导出数据"]})]}),l.jsxs("div",{className:"bg-white p-6 rounded-lg border border-gray-200",children:[l.jsxs("div",{className:"flex items-center mb-4",children:[l.jsx(mi,{className:"w-6 h-6 text-blue-600 mr-3"}),l.jsx("h3",{className:"text-lg font-medium",children:"导入数据"})]}),l.jsx("p",{className:"text-gray-600 mb-4 text-sm",children:"从备份文件恢复配置和历史记录。将覆盖当前数据。"}),l.jsxs("label",{className:"w-full flex items-center justify-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 cursor-pointer",children:[l.jsx(mi,{className:"w-4 h-4 mr-2"}),o?"导入中...":"选择文件",l.jsx("input",{type:"file",accept:".json",onChange:g,disabled:o,className:"hidden"})]})]}),l.jsxs("div",{className:"bg-white p-6 rounded-lg border border-gray-200",children:[l.jsxs("div",{className:"flex items-center mb-4",children:[l.jsx(mr,{className:"w-6 h-6 text-red-600 mr-3"}),l.jsx("h3",{className:"text-lg font-medium",children:"清除数据"})]}),l.jsx("p",{className:"text-gray-600 mb-4 text-sm",children:"清除所有数据并重置为默认状态。此操作不可撤销。"}),x?l.jsxs("div",{className:"space-y-2",children:[l.jsxs("div",{className:"flex items-center p-2 bg-red-50 border border-red-200 rounded text-sm text-red-700",children:[l.jsx(ci,{className:"w-4 h-4 mr-2"}),"确定要清除所有数据吗？"]}),l.jsxs("div",{className:"flex space-x-2",children:[l.jsx("button",{onClick:c,className:"flex-1 px-3 py-2 bg-red-600 text-white rounded text-sm hover:bg-red-700",children:"确认清除"}),l.jsx("button",{onClick:()=>y(!1),className:"flex-1 px-3 py-2 bg-gray-300 text-gray-700 rounded text-sm hover:bg-gray-400",children:"取消"})]})]}):l.jsxs("button",{onClick:()=>y(!0),className:"w-full flex items-center justify-center px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700",children:[l.jsx(mr,{className:"w-4 h-4 mr-2"}),"清除所有数据"]})]})]}),l.jsx("div",{className:"mt-8 bg-blue-50 border border-blue-200 rounded-lg p-4",children:l.jsxs("div",{className:"flex items-start",children:[l.jsx(Vm,{className:"w-5 h-5 text-blue-500 mr-2 mt-0.5"}),l.jsxs("div",{className:"text-sm text-blue-700",children:[l.jsx("div",{className:"font-medium mb-1",children:"使用说明："}),l.jsxs("ul",{className:"space-y-1 text-xs",children:[l.jsx("li",{children:"• 导出的数据包含智能体配置、LLM配置、讨论历史等所有信息"}),l.jsx("li",{children:"• 导入数据会覆盖当前所有配置，建议先导出备份"}),l.jsx("li",{children:"• 清除数据会删除所有自定义配置，但会保留默认智能体"}),l.jsx("li",{children:"• 数据存储在浏览器本地，清除浏览器数据会丢失所有配置"})]})]})]})})]})})})},Np=()=>{const{state:e,dispatch:t}=Lt(),[n,r]=T.useState(""),[s,i]=T.useState("date"),[o,a]=T.useState("all"),[u,m]=T.useState(null),[x,y]=T.useState(null),v=async g=>{if(confirm("确定要删除这条讨论记录吗？此操作不可撤销。"))try{y(g),await R.deleteDiscussion(g);const c=await R.getDiscussions();t({type:"SET_ALL_DISCUSSIONS",payload:c})}catch(c){console.error("删除讨论失败:",c),alert("删除失败，请重试")}finally{y(null)}},w=e.allDiscussions.filter(g=>{const c=g.topic.toLowerCase().includes(n.toLowerCase()),d=o==="all"||g.status===o;return c&&d}).sort((g,c)=>{switch(s){case"date":return new Date(c.createdAt).getTime()-new Date(g.createdAt).getTime();case"topic":return g.topic.localeCompare(c.topic);case"messages":return c.messages.length-g.messages.length;default:return 0}}),j=g=>{switch(g){case"consensus":return l.jsx(kn,{className:"w-4 h-4 text-green-500"});case"ended":return l.jsx(Fc,{className:"w-4 h-4 text-gray-500"});default:return l.jsx(et,{className:"w-4 h-4 text-blue-500"})}},p=g=>{switch(g){case"consensus":return"已达成共识";case"ended":return"已结束";default:return"进行中"}};return l.jsx("div",{className:"h-[calc(100vh-4rem)] bg-gradient-to-br from-gray-50 to-blue-50 w-full overflow-hidden",children:l.jsx("div",{className:"h-full p-6",children:l.jsxs("div",{className:"bg-white rounded-xl shadow-lg h-full flex flex-col",children:[l.jsxs("div",{className:"bg-gradient-to-r from-purple-600 to-blue-600 text-white p-6 rounded-t-xl",children:[l.jsxs("div",{className:"flex items-center gap-3 mb-4",children:[l.jsx(Ns,{size:32}),l.jsxs("div",{children:[l.jsx("h1",{className:"text-2xl font-bold",children:"讨论历史"}),l.jsx("p",{className:"text-purple-100",children:"查看和管理历史讨论记录"})]})]}),l.jsxs("div",{className:"flex gap-4 items-center",children:[l.jsxs("div",{className:"flex-1 relative",children:[l.jsx(Qm,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4"}),l.jsx("input",{type:"text",placeholder:"搜索讨论话题...",value:n,onChange:g=>r(g.target.value),className:"w-full pl-10 pr-4 py-2 rounded-lg text-gray-900 placeholder-gray-500"})]}),l.jsxs("select",{value:s,onChange:g=>i(g.target.value),className:"px-4 py-2 rounded-lg text-gray-900",children:[l.jsx("option",{value:"date",children:"按时间排序"}),l.jsx("option",{value:"topic",children:"按话题排序"}),l.jsx("option",{value:"messages",children:"按消息数排序"})]}),l.jsxs("select",{value:o,onChange:g=>a(g.target.value),className:"px-4 py-2 rounded-lg text-gray-900",children:[l.jsx("option",{value:"all",children:"全部状态"}),l.jsx("option",{value:"consensus",children:"已达成共识"}),l.jsx("option",{value:"ended",children:"已结束"})]})]})]}),l.jsx("div",{className:"flex-1 overflow-y-auto p-6",children:w.length===0?l.jsxs("div",{className:"text-center py-12",children:[l.jsx(Ns,{size:64,className:"text-gray-400 mx-auto mb-4"}),l.jsx("h3",{className:"text-xl font-semibold text-gray-900 mb-2",children:"暂无讨论记录"}),l.jsx("p",{className:"text-gray-600",children:"开始一个新的讨论来创建历史记录"})]}):l.jsx("div",{className:"space-y-4",children:w.map(g=>{const c=e.agents.filter(d=>g.participants.includes(d.id));return l.jsx("div",{className:"bg-gray-50 rounded-lg border border-gray-200",children:l.jsxs("div",{className:"p-4",children:[l.jsxs("div",{className:"flex items-center justify-between",children:[l.jsxs("div",{className:"flex-1",children:[l.jsxs("div",{className:"flex items-center gap-3 mb-2",children:[j(g.status),l.jsx("h3",{className:"font-semibold text-gray-900",children:g.topic}),l.jsx("span",{className:"text-sm text-gray-500",children:p(g.status)})]}),l.jsxs("div",{className:"flex items-center gap-6 text-sm text-gray-600",children:[l.jsxs("div",{className:"flex items-center gap-1",children:[l.jsx(Pm,{className:"w-4 h-4"}),new Date(g.createdAt).toLocaleString()]}),l.jsxs("div",{className:"flex items-center gap-1",children:[l.jsx(et,{className:"w-4 h-4"}),g.messages.length," 条消息"]}),l.jsxs("div",{className:"flex items-center gap-1",children:[l.jsx(Qt,{className:"w-4 h-4"}),c.length," 位参与者"]}),g.consensusScore!==void 0&&l.jsxs("div",{className:"flex items-center gap-1",children:[l.jsx(Bc,{className:"w-4 h-4"}),"共识度 ",Math.round(g.consensusScore),"%"]})]})]}),l.jsxs("div",{className:"flex items-center gap-2",children:[l.jsx("button",{onClick:()=>v(g.id),disabled:x===g.id,className:"p-2 text-gray-600 hover:text-red-600 hover:bg-red-50 rounded-md disabled:opacity-50 disabled:cursor-not-allowed transition-colors",title:"删除记录",children:x===g.id?l.jsx("div",{className:"animate-spin w-4 h-4 border-2 border-red-500 border-t-transparent rounded-full"}):l.jsx(mr,{className:"w-4 h-4"})}),l.jsx("button",{onClick:()=>m(u===g.id?null:g.id),className:"p-2 text-gray-600 hover:text-purple-600 hover:bg-purple-50 rounded-md transition-colors",title:"查看详情",children:u===g.id?l.jsx(zm,{className:"w-4 h-4"}):l.jsx(Im,{className:"w-4 h-4"})})]})]}),u===g.id&&l.jsxs("div",{className:"mt-4 pt-4 border-t border-gray-200",children:[l.jsxs("div",{className:"mb-4",children:[l.jsx("h4",{className:"font-medium text-gray-900 mb-2",children:"参与者"}),l.jsx("div",{className:"flex flex-wrap gap-2",children:c.map(d=>l.jsxs("div",{className:"flex items-center gap-2 bg-white px-3 py-1 rounded-full border",children:[l.jsx("img",{src:d.avatar,alt:d.name,className:"w-6 h-6 rounded-full object-cover"}),l.jsx("span",{className:"text-sm font-medium",children:d.name})]},d.id))})]}),g.consensus&&l.jsxs("div",{className:"mb-4",children:[l.jsx("h4",{className:"font-medium text-gray-900 mb-2",children:"讨论结论"}),l.jsx("div",{className:"bg-green-50 border border-green-200 rounded-lg p-3",children:l.jsx("p",{className:"text-green-800",children:g.consensus})})]}),l.jsxs("div",{children:[l.jsx("h4",{className:"font-medium text-gray-900 mb-2",children:"消息预览"}),l.jsxs("div",{className:"space-y-2 max-h-60 overflow-y-auto",children:[g.messages.slice(0,5).map(d=>{const f=c.find(h=>h.id===d.agentId);return l.jsxs("div",{className:"bg-white p-3 rounded border",children:[l.jsxs("div",{className:"flex items-center gap-2 mb-1",children:[l.jsx("span",{className:"font-medium text-sm",children:f==null?void 0:f.name}),l.jsx("span",{className:"text-xs text-gray-500",children:new Date(d.timestamp).toLocaleTimeString()})]}),l.jsx("p",{className:"text-sm text-gray-700 line-clamp-2",children:d.content})]},d.id)}),g.messages.length>5&&l.jsxs("div",{className:"text-center text-sm text-gray-500",children:["还有 ",g.messages.length-5," 条消息..."]})]})]})]})]})},g.id)})})})]})})})},Sp=()=>{const{state:e}=Lt(),[t,n]=T.useState(!1),[r,s]=T.useState([{id:"storage",label:"初始化存储服务",status:"pending"},{id:"server",label:"检查服务器连接",status:"pending"},{id:"agents",label:"加载智能体配置",status:"pending"},{id:"llm",label:"加载LLM配置",status:"pending"},{id:"discussions",label:"加载讨论历史",status:"pending"}]);T.useEffect(()=>{s(u=>u.map(m=>{const x=u.findIndex(v=>v.id===m.id),y=u.findIndex(v=>v.id===e.loadingStep);return x<y?{...m,status:"completed"}:x===y?{...m,status:"loading"}:{...m,status:"pending"}}))},[e.loadingStep]),T.useEffect(()=>{const u=setTimeout(()=>{n(!0)},1e4);return()=>clearTimeout(u)},[]);const i=u=>{switch(u){case"completed":return l.jsx(kn,{className:"w-4 h-4 text-green-500"});case"loading":return l.jsx(ja,{className:"w-4 h-4 text-blue-500 animate-spin"});case"error":return l.jsx(Sn,{className:"w-4 h-4 text-red-500"});default:return l.jsx(Fc,{className:"w-4 h-4 text-gray-400"})}},o=r.filter(u=>u.status==="completed").length,a=o/r.length*100;return l.jsx("div",{className:"min-h-screen bg-gradient-to-br from-indigo-50 via-blue-50 to-purple-50 flex items-center justify-center",children:l.jsxs("div",{className:"text-center max-w-md mx-auto",children:[l.jsxs("div",{className:"flex items-center justify-center gap-3 mb-8",children:[l.jsx(jt,{size:48,className:"text-blue-600 animate-pulse"}),l.jsx("h1",{className:"text-4xl font-bold text-gray-900",children:"多智能体讨论系统"})]}),l.jsxs("div",{className:"flex items-center justify-center gap-3 mb-6",children:[l.jsx(ja,{className:"w-6 h-6 text-blue-600 animate-spin"}),l.jsx("span",{className:"text-lg text-gray-600",children:"正在初始化系统..."})]}),l.jsxs("div",{className:"w-80 mx-auto mb-6",children:[l.jsx("div",{className:"w-full bg-gray-200 rounded-full h-2",children:l.jsx("div",{className:"bg-blue-500 h-2 rounded-full transition-all duration-500",style:{width:`${a}%`}})}),l.jsxs("div",{className:"mt-2 text-sm text-gray-500",children:[o,"/",r.length," 步骤完成"]})]}),l.jsx("div",{className:"space-y-3 text-sm",children:r.map(u=>l.jsxs("div",{className:"flex items-center justify-center gap-3",children:[i(u.status),l.jsx("span",{className:`${u.status==="completed"?"text-green-600":u.status==="loading"?"text-blue-600":u.status==="error"?"text-red-600":"text-gray-500"}`,children:u.label})]},u.id))}),t&&l.jsxs("div",{className:"mt-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg",children:[l.jsxs("div",{className:"flex items-center gap-2 text-yellow-800",children:[l.jsx(Sn,{className:"w-5 h-5"}),l.jsx("span",{className:"font-medium",children:"加载时间较长"})]}),l.jsx("p",{className:"text-sm text-yellow-700 mt-1",children:"连接服务器超时，原因是网络较慢或后台服务不可用。"})]})]})})};class kp extends T.Component{constructor(n){super(n);Le(this,"handleReload",()=>{window.location.reload()});Le(this,"handleReset",()=>{this.setState({hasError:!1,error:null,errorInfo:null})});this.state={hasError:!1,error:null,errorInfo:null}}static getDerivedStateFromError(n){return{hasError:!0,error:n,errorInfo:null}}componentDidCatch(n,r){console.error("ErrorBoundary caught an error:",n,r),this.setState({error:n,errorInfo:r})}render(){var n,r,s;if(this.state.hasError){const i=(r=(n=this.state.error)==null?void 0:n.message)==null?void 0:r.includes("无法连接到后端服务器");return l.jsx("div",{className:"min-h-screen bg-gradient-to-br from-red-50 via-orange-50 to-yellow-50 flex items-center justify-center",children:l.jsxs("div",{className:"text-center max-w-2xl mx-auto p-8",children:[l.jsxs("div",{className:"flex items-center justify-center gap-3 mb-6",children:[l.jsx(ci,{size:48,className:"text-red-500"}),l.jsx("h1",{className:"text-3xl font-bold text-gray-900",children:i?"无法连接后端服务":"系统初始化失败"})]}),l.jsxs("div",{className:"bg-white rounded-lg shadow-lg p-6 mb-6",children:[l.jsx("h2",{className:"text-lg font-semibold text-gray-800 mb-4",children:"错误详情"}),l.jsx("div",{className:"text-left bg-gray-50 rounded p-4 mb-4",children:l.jsx("p",{className:"text-red-600 font-mono text-sm",children:((s=this.state.error)==null?void 0:s.message)||"未知错误"})}),l.jsx("div",{className:"text-sm text-gray-600 mb-4",children:i?l.jsxs(l.Fragment,{children:[l.jsx("p",{className:"font-medium mb-2",children:"请检查以下项目："}),l.jsxs("ul",{className:"list-disc list-inside space-y-1",children:[l.jsx("li",{children:"后端服务是否已启动（端口5000）"}),l.jsx("li",{children:"网络连接是否正常"}),l.jsx("li",{children:"防火墙是否阻止了连接"}),l.jsx("li",{children:"后端服务地址配置是否正确"})]})]}):l.jsxs(l.Fragment,{children:[l.jsx("p",{children:"可能的原因："}),l.jsxs("ul",{className:"list-disc list-inside mt-2 space-y-1",children:[l.jsx("li",{children:"网络连接问题"}),l.jsx("li",{children:"后端服务器不可用"}),l.jsx("li",{children:"浏览器存储空间不足"}),l.jsx("li",{children:"配置文件损坏"})]})]})})]}),l.jsxs("div",{className:"flex gap-4 justify-center",children:[l.jsxs("button",{onClick:this.handleReload,className:"flex items-center gap-2 px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:[l.jsx(Bm,{size:20}),"重新加载页面"]}),l.jsx("button",{onClick:this.handleReset,className:"flex items-center gap-2 px-6 py-3 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors",children:"重试初始化"})]}),i&&l.jsxs("div",{className:"mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg",children:[l.jsx("p",{className:"text-blue-800 font-medium mb-2",children:"启动后端服务："}),l.jsxs("div",{className:"text-sm text-blue-700 space-y-1",children:[l.jsxs("p",{children:["Windows: 运行 ",l.jsx("code",{className:"bg-blue-100 px-1 rounded",children:"start.bat"})]}),l.jsxs("p",{children:["Linux/macOS: 运行 ",l.jsx("code",{className:"bg-blue-100 px-1 rounded",children:"./start.sh"})]})]})]})]})})}return this.props.children}}function Cp(){const{state:e}=Lt(),[t,n]=T.useState("home");if(za.useEffect(()=>{e.isDiscussionActive&&e.currentDiscussion&&n("discussion")},[e.isDiscussionActive,e.currentDiscussion]),e.isLoading)return l.jsx(Sp,{});const r=()=>{switch(t){case"agents":return l.jsx(sp,{});case"llm":return l.jsx(wp,{});case"data":return l.jsx(jp,{});case"history":return l.jsx(Np,{});case"setup":return l.jsx(op,{});case"discussion":return l.jsx(xp,{});default:return l.jsx(bp,{onNavigate:n})}};return l.jsxs("div",{className:"h-screen bg-gray-50 w-full flex flex-col",children:[t!=="home"&&l.jsx("nav",{className:"flex-shrink-0 bg-white shadow-sm border-b border-gray-200 w-full",children:l.jsx("div",{className:"w-full px-6",children:l.jsxs("div",{className:"flex items-center justify-between h-16",children:[l.jsxs("div",{className:"flex items-center gap-8",children:[l.jsxs("button",{onClick:()=>n("home"),className:"flex items-center gap-2 text-gray-900 hover:text-blue-600 font-medium",children:[l.jsx(jt,{size:24,className:"text-blue-600"}),"多智能体讨论系统"]}),l.jsxs("div",{className:"flex gap-6",children:[l.jsxs("button",{onClick:()=>n("agents"),className:`flex items-center gap-2 px-3 py-2 rounded-md transition-colors ${t==="agents"?"bg-blue-100 text-blue-700":"text-gray-600 hover:text-gray-900"}`,children:[l.jsx(Qt,{size:20}),"智能体管理"]}),l.jsxs("button",{onClick:()=>n("llm"),className:`flex items-center gap-2 px-3 py-2 rounded-md transition-colors ${t==="llm"?"bg-orange-100 text-orange-700":"text-gray-600 hover:text-gray-900"}`,children:[l.jsx(Vc,{size:20}),"LLM管理"]}),l.jsxs("button",{onClick:()=>n("history"),className:`flex items-center gap-2 px-3 py-2 rounded-md transition-colors ${t==="history"?"bg-purple-100 text-purple-700":"text-gray-600 hover:text-gray-900"}`,children:[l.jsx(Ns,{size:20}),"讨论历史"]}),l.jsxs("button",{onClick:()=>n("data"),className:`flex items-center gap-2 px-3 py-2 rounded-md transition-colors ${t==="data"?"bg-indigo-100 text-indigo-700":"text-gray-600 hover:text-gray-900"}`,children:[l.jsx(uo,{size:20}),"数据管理"]}),l.jsxs("button",{onClick:()=>n("setup"),className:`flex items-center gap-2 px-3 py-2 rounded-md transition-colors ${t==="setup"?"bg-blue-100 text-blue-700":"text-gray-600 hover:text-gray-900"}`,disabled:e.agents.length===0,children:[l.jsx(Nt,{size:20}),"创建讨论"]}),e.isDiscussionActive&&l.jsxs("button",{onClick:()=>n("discussion"),className:`flex items-center gap-2 px-3 py-2 rounded-md transition-colors ${t==="discussion"?"bg-green-100 text-green-700":"text-green-600 hover:text-green-700"}`,children:[l.jsx(et,{size:20}),"讨论进行中",l.jsx("div",{className:"w-2 h-2 bg-green-500 rounded-full animate-pulse"})]})]})]}),l.jsxs("div",{className:"flex items-center gap-4",children:[l.jsxs("span",{className:"text-sm text-gray-500",children:[e.agents.length," 个智能体"]}),e.isDiscussionActive&&l.jsxs("div",{className:"flex items-center gap-2 bg-green-50 text-green-700 px-3 py-1 rounded-full text-sm",children:[l.jsx("div",{className:"w-2 h-2 bg-green-500 rounded-full"}),"讨论中"]})]})]})})}),l.jsx("div",{className:"flex-1 overflow-hidden",children:r()})]})}function bp({onNavigate:e}){var n;const{state:t}=Lt();return l.jsx("div",{className:"h-full bg-gradient-to-br from-indigo-50 via-blue-50 to-purple-50 w-full fixed-layout overflow-y-auto",children:l.jsxs("div",{className:"w-full px-6 py-12 fixed-layout",children:[l.jsxs("div",{className:"text-center mb-16",children:[l.jsxs("div",{className:"flex items-center justify-center gap-3 mb-6",children:[l.jsx(jt,{size:48,className:"text-blue-600"}),l.jsx("h1",{className:"text-5xl font-bold text-gray-900",children:"多智能体讨论系统"})]}),l.jsx("p",{className:"text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed",children:"通过配置不同专业背景和思维方式的AI智能体，创建富有洞察力的讨论环境， 探索复杂问题的多维度解决方案，并达成有价值的共识。"}),l.jsxs("div",{className:"flex items-center justify-center gap-8 mt-8 text-sm text-gray-500",children:[l.jsxs("div",{className:"flex items-center gap-2",children:[l.jsx(Sa,{size:16,className:"text-yellow-500"}),"支持2-8个智能体同时讨论"]}),l.jsxs("div",{className:"flex items-center gap-2",children:[l.jsx(jt,{size:16,className:"text-purple-500"}),"智能共识判断算法"]}),l.jsxs("div",{className:"flex items-center gap-2",children:[l.jsx(et,{size:16,className:"text-blue-500"}),"实时讨论模拟"]})]})]}),l.jsxs("div",{className:"space-y-8 mb-16",children:[l.jsxs("div",{className:"flex flex-wrap justify-center gap-6",children:[l.jsx("div",{className:"group",children:l.jsxs("div",{className:"bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all p-8 border border-gray-100 group-hover:border-blue-200 fixed-size-card",children:[l.jsxs("div",{className:"flex items-center gap-4 mb-6",children:[l.jsx("div",{className:"w-16 h-16 bg-blue-100 rounded-xl flex items-center justify-center group-hover:bg-blue-200 transition-colors",children:l.jsx(Qt,{size:32,className:"text-blue-600"})}),l.jsxs("div",{children:[l.jsx("h3",{className:"text-xl font-bold text-gray-900",children:"智能体管理"}),l.jsx("p",{className:"text-gray-500",children:"配置AI智能体"})]})]}),l.jsx("p",{className:"text-gray-600 mb-6 leading-relaxed",children:"创建和配置具有不同专业背景、思维方式和性格特征的智能体。 每个智能体都有独特的知识领域和讨论风格。"}),l.jsxs("div",{className:"mb-6",children:[l.jsxs("div",{className:"flex items-center justify-between text-sm text-gray-500 mb-2",children:[l.jsx("span",{children:"当前智能体数量"}),l.jsxs("span",{className:"font-bold text-blue-600 text-lg",children:[t.agents.length,"/8"]})]}),l.jsx("div",{className:"w-full h-2 bg-gray-200 rounded-full",children:l.jsx("div",{className:"h-full bg-blue-500 rounded-full transition-all",style:{width:`${t.agents.length/8*100}%`}})})]}),l.jsx("button",{onClick:()=>e("agents"),className:"w-full bg-blue-600 text-white py-3 rounded-xl hover:bg-blue-700 transition-colors font-medium",children:"管理智能体"})]})}),l.jsx("div",{className:"group",children:l.jsxs("div",{className:"bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all p-8 border border-gray-100 group-hover:border-orange-200 fixed-size-card",children:[l.jsxs("div",{className:"flex items-center gap-4 mb-6",children:[l.jsx("div",{className:"w-16 h-16 bg-orange-100 rounded-xl flex items-center justify-center group-hover:bg-orange-200 transition-colors",children:l.jsx(Vc,{size:32,className:"text-orange-600"})}),l.jsxs("div",{children:[l.jsx("h3",{className:"text-xl font-bold text-gray-900",children:"LLM管理"}),l.jsx("p",{className:"text-gray-500",children:"配置大语言模型"})]})]}),l.jsx("p",{className:"text-gray-600 mb-6 leading-relaxed",children:"配置和管理大语言模型，为智能体提供真实的AI对话能力。 支持OpenAI、Anthropic等多种提供商。"}),l.jsxs("div",{className:"space-y-3 mb-6",children:[l.jsxs("div",{className:"flex items-center gap-2 text-sm text-gray-600",children:[l.jsx("div",{className:"w-2 h-2 bg-green-500 rounded-full"}),"支持多种LLM提供商"]}),l.jsxs("div",{className:"flex items-center gap-2 text-sm text-gray-600",children:[l.jsx("div",{className:"w-2 h-2 bg-blue-500 rounded-full"}),"个性化配置参数"]}),l.jsxs("div",{className:"flex items-center gap-2 text-sm text-gray-600",children:[l.jsx("div",{className:"w-2 h-2 bg-purple-500 rounded-full"}),"连接测试功能"]})]}),l.jsx("button",{onClick:()=>e("llm"),className:"w-full bg-orange-600 text-white py-3 rounded-xl hover:bg-orange-700 transition-colors font-medium",children:"管理LLM配置"})]})}),l.jsx("div",{className:"group",children:l.jsxs("div",{className:"bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all p-8 border border-gray-100 group-hover:border-indigo-200 fixed-size-card",children:[l.jsxs("div",{className:"flex items-center gap-4 mb-6",children:[l.jsx("div",{className:"w-16 h-16 bg-indigo-100 rounded-xl flex items-center justify-center group-hover:bg-indigo-200 transition-colors",children:l.jsx(uo,{size:32,className:"text-indigo-600"})}),l.jsxs("div",{children:[l.jsx("h3",{className:"text-xl font-bold text-gray-900",children:"数据管理"}),l.jsx("p",{className:"text-gray-500",children:"备份与恢复"})]})]}),l.jsx("p",{className:"text-gray-600 mb-6 leading-relaxed",children:"管理系统数据的备份、恢复和清理，确保配置和历史记录的安全。"}),l.jsxs("div",{className:"space-y-3 mb-6",children:[l.jsxs("div",{className:"flex items-center gap-2 text-sm text-gray-600",children:[l.jsx("div",{className:"w-2 h-2 bg-green-500 rounded-full"}),"数据导出备份"]}),l.jsxs("div",{className:"flex items-center gap-2 text-sm text-gray-600",children:[l.jsx("div",{className:"w-2 h-2 bg-blue-500 rounded-full"}),"配置导入恢复"]}),l.jsxs("div",{className:"flex items-center gap-2 text-sm text-gray-600",children:[l.jsx("div",{className:"w-2 h-2 bg-red-500 rounded-full"}),"数据清理重置"]})]}),l.jsx("button",{onClick:()=>e("data"),className:"w-full bg-indigo-600 text-white py-3 rounded-xl hover:bg-indigo-700 transition-colors font-medium",children:"管理数据"})]})})]}),l.jsxs("div",{className:"flex flex-wrap justify-center gap-6",children:[l.jsx("div",{className:"group",children:l.jsxs("div",{className:"bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all p-8 border border-gray-100 group-hover:border-purple-200 fixed-size-card",children:[l.jsxs("div",{className:"flex items-center gap-4 mb-6",children:[l.jsx("div",{className:"w-16 h-16 bg-purple-100 rounded-xl flex items-center justify-center group-hover:bg-purple-200 transition-colors",children:l.jsx(Nt,{size:32,className:"text-purple-600"})}),l.jsxs("div",{children:[l.jsx("h3",{className:"text-xl font-bold text-gray-900",children:"创建讨论"}),l.jsx("p",{className:"text-gray-500",children:"配置讨论参数"})]})]}),l.jsx("p",{className:"text-gray-600 mb-6 leading-relaxed",children:"设置讨论话题、选择参与的智能体、配置讨论模式， 开始一场富有见解的AI讨论。"}),l.jsxs("div",{className:"space-y-3 mb-6",children:[l.jsxs("div",{className:"flex items-center gap-2 text-sm text-gray-600",children:[l.jsx("div",{className:"w-2 h-2 bg-green-500 rounded-full"}),"自由讨论模式"]}),l.jsxs("div",{className:"flex items-center gap-2 text-sm text-gray-600",children:[l.jsx("div",{className:"w-2 h-2 bg-blue-500 rounded-full"}),"主持人模式"]}),l.jsxs("div",{className:"flex items-center gap-2 text-sm text-gray-600",children:[l.jsx("div",{className:"w-2 h-2 bg-orange-500 rounded-full"}),"智能共识判断"]})]}),l.jsx("button",{onClick:()=>e("setup"),disabled:t.agents.length<2,className:"w-full bg-purple-600 text-white py-3 rounded-xl hover:bg-purple-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors font-medium",children:t.agents.length<2?"需要至少2个智能体":"创建新讨论"})]})}),l.jsx("div",{className:"group",children:l.jsxs("div",{className:"bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all p-8 border border-gray-100 group-hover:border-green-200 fixed-size-card",children:[l.jsxs("div",{className:"flex items-center gap-4 mb-6",children:[l.jsx("div",{className:"w-16 h-16 bg-green-100 rounded-xl flex items-center justify-center group-hover:bg-green-200 transition-colors",children:l.jsx(et,{size:32,className:"text-green-600"})}),l.jsxs("div",{children:[l.jsx("h3",{className:"text-xl font-bold text-gray-900",children:"讨论状态"}),l.jsx("p",{className:"text-gray-500",children:"实时监控"})]})]}),t.isDiscussionActive?l.jsxs(l.Fragment,{children:[l.jsx("p",{className:"text-gray-600 mb-6 leading-relaxed",children:"当前有一场讨论正在进行中，您可以实时观看智能体之间的对话， 监控共识度变化。"}),l.jsxs("div",{className:"bg-green-50 border border-green-200 rounded-lg p-4 mb-6",children:[l.jsxs("div",{className:"flex items-center gap-2 mb-2",children:[l.jsx("div",{className:"w-2 h-2 bg-green-500 rounded-full animate-pulse"}),l.jsx("span",{className:"font-medium text-green-800",children:"讨论进行中"})]}),l.jsxs("p",{className:"text-green-700 text-sm",children:["话题：",(n=t.currentDiscussion)==null?void 0:n.topic]})]}),l.jsx("button",{onClick:()=>e("discussion"),className:"w-full bg-green-600 text-white py-3 rounded-xl hover:bg-green-700 transition-colors font-medium",children:"进入讨论室"})]}):l.jsxs(l.Fragment,{children:[l.jsx("p",{className:"text-gray-600 mb-6 leading-relaxed",children:"目前没有进行中的讨论。创建智能体并配置讨论参数后， 您就可以开始一场精彩的AI对话。"}),l.jsxs("div",{className:"bg-gray-50 border border-gray-200 rounded-lg p-4 mb-6",children:[l.jsxs("div",{className:"flex items-center gap-2 mb-2",children:[l.jsx("div",{className:"w-2 h-2 bg-gray-400 rounded-full"}),l.jsx("span",{className:"font-medium text-gray-600",children:"空闲状态"})]}),l.jsxs("p",{className:"text-gray-500 text-sm",children:["历史讨论：",t.allDiscussions.length," 场"]})]}),l.jsx("button",{onClick:()=>e(t.agents.length<2?"agents":"setup"),className:"w-full bg-gray-400 text-white py-3 rounded-xl hover:bg-gray-500 transition-colors font-medium",children:t.agents.length<2?"先创建智能体":"开始新讨论"})]})]})}),l.jsx("div",{className:"group",children:l.jsxs("div",{className:"bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all p-8 border border-gray-100 group-hover:border-purple-200 fixed-size-card",children:[l.jsxs("div",{className:"flex items-center gap-4 mb-6",children:[l.jsx("div",{className:"w-16 h-16 bg-purple-100 rounded-xl flex items-center justify-center group-hover:bg-purple-200 transition-colors",children:l.jsx(Ns,{size:32,className:"text-purple-600"})}),l.jsxs("div",{children:[l.jsx("h3",{className:"text-xl font-bold text-gray-900",children:"讨论历史"}),l.jsx("p",{className:"text-gray-500",children:"查看历史记录"})]})]}),l.jsx("p",{className:"text-gray-600 mb-6 leading-relaxed",children:"查看和分析历史讨论记录，了解智能体的对话模式和共识形成过程。"}),l.jsxs("div",{className:"mb-6",children:[l.jsxs("div",{className:"flex items-center justify-between text-sm text-gray-500 mb-2",children:[l.jsx("span",{children:"历史讨论数量"}),l.jsx("span",{className:"font-bold text-purple-600 text-lg",children:t.allDiscussions.length})]}),l.jsx("div",{className:"w-full h-2 bg-gray-200 rounded-full",children:l.jsx("div",{className:"h-full bg-purple-500 rounded-full transition-all",style:{width:`${Math.min(t.allDiscussions.length/10*100,100)}%`}})})]}),l.jsx("button",{onClick:()=>e("history"),className:"w-full bg-purple-600 text-white py-3 rounded-xl hover:bg-purple-700 transition-colors font-medium",children:"查看历史"})]})})]})]}),l.jsxs("div",{className:"bg-white rounded-2xl shadow-xl p-12 border border-gray-100",children:[l.jsx("h2",{className:"text-3xl font-bold text-gray-900 text-center mb-8",children:"系统特色功能"}),l.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8",children:[l.jsxs("div",{className:"text-center",children:[l.jsx("div",{className:"w-16 h-16 bg-blue-100 rounded-xl flex items-center justify-center mx-auto mb-4",children:l.jsx(jt,{size:32,className:"text-blue-600"})}),l.jsx("h3",{className:"font-semibold text-gray-900 mb-2",children:"智能化对话"}),l.jsx("p",{className:"text-gray-600 text-sm",children:"基于专业领域和性格特征生成真实的对话内容"})]}),l.jsxs("div",{className:"text-center",children:[l.jsx("div",{className:"w-16 h-16 bg-green-100 rounded-xl flex items-center justify-center mx-auto mb-4",children:l.jsx(Sa,{size:32,className:"text-green-600"})}),l.jsx("h3",{className:"font-semibold text-gray-900 mb-2",children:"实时共识监控"}),l.jsx("p",{className:"text-gray-600 text-sm",children:"动态计算讨论共识度，智能判断达成一致的时机"})]}),l.jsxs("div",{className:"text-center",children:[l.jsx("div",{className:"w-16 h-16 bg-purple-100 rounded-xl flex items-center justify-center mx-auto mb-4",children:l.jsx(Qt,{size:32,className:"text-purple-600"})}),l.jsx("h3",{className:"font-semibold text-gray-900 mb-2",children:"多模式讨论"}),l.jsx("p",{className:"text-gray-600 text-sm",children:"支持自由讨论和主持人模式，适应不同讨论需求"})]}),l.jsxs("div",{className:"text-center",children:[l.jsx("div",{className:"w-16 h-16 bg-orange-100 rounded-xl flex items-center justify-center mx-auto mb-4",children:l.jsx(et,{size:32,className:"text-orange-600"})}),l.jsx("h3",{className:"font-semibold text-gray-900 mb-2",children:"丰富的交互"}),l.jsx("p",{className:"text-gray-600 text-sm",children:"支持多种消息类型，包括陈述、提问、同意、反对"})]})]})]})]})})}function Ep(){return l.jsx(kp,{children:l.jsx(Tm,{children:l.jsx(Cp,{})})})}hl.createRoot(document.getElementById("root")).render(l.jsx(za.StrictMode,{children:l.jsx(Ep,{})}));
