#!/usr/bin/env python3
"""
数据库迁移脚本：为Discussion表添加end_reason字段
"""

import sqlite3
import os
import sys

def add_end_reason_column():
    """为Discussion表添加end_reason字段"""
    
    # 获取数据库路径
    db_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'instance', 'multi_agent_system.db')
    
    if not os.path.exists(db_path):
        print(f"数据库文件不存在: {db_path}")
        return False
    
    try:
        # 连接数据库
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 检查字段是否已存在
        cursor.execute("PRAGMA table_info(discussions)")
        columns = [column[1] for column in cursor.fetchall()]
        
        if 'end_reason' in columns:
            print("end_reason字段已存在，无需添加")
            conn.close()
            return True
        
        # 添加end_reason字段
        cursor.execute("ALTER TABLE discussions ADD COLUMN end_reason VARCHAR(255)")
        
        # 提交更改
        conn.commit()
        print("成功添加end_reason字段到discussions表")
        
        # 验证字段是否添加成功
        cursor.execute("PRAGMA table_info(discussions)")
        columns = [column[1] for column in cursor.fetchall()]
        
        if 'end_reason' in columns:
            print("字段添加验证成功")
        else:
            print("字段添加验证失败")
            return False
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"添加字段时发生错误: {e}")
        return False

if __name__ == "__main__":
    success = add_end_reason_column()
    sys.exit(0 if success else 1)
